2025-06-14 04:29:10.698 [info] 




2025-06-14 04:29:10.711 [info] Extension host agent started.
2025-06-14 04:29:11.150 [info] [<unknown>][681e8d1a][ExtensionHostConnection] New connection established.
2025-06-14 04:29:11.151 [info] [<unknown>][81b182a4][ManagementConnection] New connection established.
2025-06-14 04:29:11.321 [info] [<unknown>][681e8d1a][ExtensionHostConnection] <937> Launched Extension Host Process.
2025-06-14 04:29:11.831 [info] ComputeTargetPlatform: linux-x64
2025-06-14 04:29:12.159 [info] Deleted marked for removal extension from disk github.copilot /home/<USER>/.vscode-server/extensions/github.copilot-1.333.0
2025-06-14 04:29:13.904 [info] ComputeTargetPlatform: linux-x64
2025-06-14 04:29:15.867 [info] Getting Manifest... augment.vscode-augment
2025-06-14 04:29:15.985 [info] Installing extension: augment.vscode-augment {"installPreReleaseVersion":true,"productVersion":{"version":"1.101.0","date":"2025-06-11T15:00:50.123Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"donotVerifySignature":false,"context":{"clientTargetPlatform":"darwin-arm64"},"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"}}
2025-06-14 04:29:18.761 [info] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 1797ms.
2025-06-14 04:29:19.875 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.0: augment.vscode-augment
2025-06-14 04:29:19.909 [info] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.0
2025-06-14 04:29:19.931 [info] Marked extension as removed augment.vscode-augment-0.481.0
2025-06-14 04:29:19.965 [info] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-14 04:33:24.167 [info] [<unknown>][81b182a4][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-06-14 04:33:24.337 [info] [<unknown>][681e8d1a][ExtensionHostConnection] <937> Extension Host Process exited with code: 0, signal: null.
2025-06-14 04:33:24.338 [info] Cancelling previous shutdown timeout
2025-06-14 04:33:24.338 [info] Last EH closed, waiting before shutting down
2025-06-14 04:33:26.707 [info] [<unknown>][8df98a51][ExtensionHostConnection] New connection established.
2025-06-14 04:33:26.708 [info] [<unknown>][7b4edd85][ManagementConnection] New connection established.
2025-06-14 04:33:26.712 [info] [<unknown>][8df98a51][ExtensionHostConnection] <1608> Launched Extension Host Process.
2025-06-14 04:33:30.929 [error] CodeExpectedError: Could not find pty 11 on pty host
    at O.W (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js:46:6391)
    at O.updateTitle (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js:46:1839)
    at R.s.<computed> (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js:45:2933)
    at Object.call (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js:28:4204)
    at ml.s (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js:26:81286)
    at ml.q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80809)
    at ps.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80211)
    at E.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2373)
    at E.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2591)
    at process.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js:24:30052)
    at process.emit (node:events:518:28)
    at emit (node:internal/child_process:949:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)
2025-06-14 04:33:30.931 [error] CodeExpectedError: Could not find pty 11 on pty host
    at O.W (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js:46:6391)
    at O.updateIcon (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js:46:1889)
    at R.s.<computed> (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js:45:2933)
    at Object.call (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js:28:4204)
    at ml.s (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js:26:81286)
    at ml.q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80809)
    at ps.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80211)
    at E.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2373)
    at E.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2591)
    at process.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/ptyHostMain.js:24:30052)
    at process.emit (node:events:518:28)
    at emit (node:internal/child_process:949:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)
2025-06-14 04:38:24.339 [info] New EH opened, aborting shutdown
