2025-06-14 04:29:20.896 [info] [main] Log level: Info
2025-06-14 04:29:20.896 [info] [main] Validating found git in: "git"
2025-06-14 04:29:20.896 [info] [main] Using git "2.47.2" from "git"
2025-06-14 04:29:20.897 [info] [Model][doInitialScan] Initial repository scan started
2025-06-14 04:29:20.897 [info] > git rev-parse --show-toplevel [70ms]
2025-06-14 04:29:20.897 [info] > git rev-parse --git-dir --git-common-dir [6ms]
2025-06-14 04:29:20.897 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-14 04:29:20.897 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-14 04:29:20.900 [info] > git config --get commit.template [23ms]
2025-06-14 04:29:20.901 [info] > git rev-parse --show-toplevel [18ms]
2025-06-14 04:29:21.395 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [483ms]
2025-06-14 04:29:21.404 [info] > git rev-parse --show-toplevel [170ms]
2025-06-14 04:29:21.436 [info] > git status -z -uall [6ms]
2025-06-14 04:29:21.436 [info] > git rev-parse --show-toplevel [21ms]
2025-06-14 04:29:22.055 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [621ms]
2025-06-14 04:29:22.447 [info] > git check-ignore -v -z --stdin [351ms]
2025-06-14 04:29:22.450 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [377ms]
2025-06-14 04:29:22.459 [info] > git rev-parse --show-toplevel [1008ms]
2025-06-14 04:29:22.537 [info] > git config --get commit.template [336ms]
2025-06-14 04:29:22.539 [info] > git rev-parse --show-toplevel [68ms]
2025-06-14 04:29:22.539 [info] > git config --get --local branch.main.vscode-merge-base [80ms]
2025-06-14 04:29:22.559 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [12ms]
2025-06-14 04:29:22.570 [info] > git rev-parse --show-toplevel [11ms]
2025-06-14 04:29:22.571 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [18ms]
2025-06-14 04:29:22.578 [info] > git merge-base refs/heads/main refs/remotes/origin/main [13ms]
2025-06-14 04:29:22.585 [info] > git rev-parse --show-toplevel [8ms]
2025-06-14 04:29:22.632 [info] > git status -z -uall [17ms]
2025-06-14 04:29:22.632 [info] > git diff --name-status -z --diff-filter=ADMR a9760fde6fc081c302411d62a5d4c72b5a1424fd...refs/remotes/origin/main [48ms]
2025-06-14 04:29:22.639 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-14 04:29:22.642 [info] > git rev-parse --show-toplevel [39ms]
2025-06-14 04:29:22.656 [info] > git rev-parse --show-toplevel [2ms]
2025-06-14 04:29:22.662 [info] > git rev-parse --show-toplevel [1ms]
2025-06-14 04:29:22.665 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-14 04:29:23.024 [info] > git show --textconv :server/csvLogger.ts [11ms]
2025-06-14 04:29:23.024 [info] > git ls-files --stage -- server/csvLogger.ts [7ms]
2025-06-14 04:29:23.042 [info] > git cat-file -s 1fce6f51d7d389e752a39c0b2b781e52270f5632 [12ms]
2025-06-14 04:29:24.125 [info] > git config --get commit.template [32ms]
2025-06-14 04:29:24.165 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [23ms]
2025-06-14 04:29:24.281 [info] > git status -z -uall [17ms]
2025-06-14 04:29:24.282 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [14ms]
2025-06-14 04:29:25.113 [info] > git config --get --local branch.main.github-pr-owner-number [144ms]
2025-06-14 04:29:25.113 [warning] [Git][config] git config failed: Failed to execute git
2025-06-14 04:29:27.833 [info] > git fetch [231ms]
2025-06-14 04:29:27.833 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/eddie333016/BeamTechLandingPage/'
2025-06-14 04:29:27.844 [info] > git config --get commit.template [5ms]
2025-06-14 04:29:27.845 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:29:27.854 [info] > git status -z -uall [4ms]
2025-06-14 04:29:27.856 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:29:29.426 [info] > git ls-files --stage -- server/csvLogger.ts [1ms]
2025-06-14 04:29:29.432 [info] > git cat-file -s 1fce6f51d7d389e752a39c0b2b781e52270f5632 [2ms]
2025-06-14 04:29:29.651 [info] > git show --textconv :server/csvLogger.ts [2ms]
2025-06-14 04:29:32.608 [info] > git config --get commit.template [30ms]
2025-06-14 04:29:32.621 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:29:32.643 [info] > git status -z -uall [12ms]
2025-06-14 04:29:32.644 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:29:37.662 [info] > git config --get commit.template [6ms]
2025-06-14 04:29:37.663 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:29:37.673 [info] > git status -z -uall [5ms]
2025-06-14 04:29:37.674 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:29:42.690 [info] > git config --get commit.template [6ms]
2025-06-14 04:29:42.691 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:29:42.703 [info] > git status -z -uall [6ms]
2025-06-14 04:29:42.704 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:29:54.657 [info] > git config --get commit.template [10ms]
2025-06-14 04:29:54.658 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 04:29:54.684 [info] > git status -z -uall [14ms]
2025-06-14 04:29:54.685 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-14 04:33:08.971 [info] > git config --get commit.template [5ms]
2025-06-14 04:33:08.973 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 04:33:08.985 [info] > git status -z -uall [6ms]
2025-06-14 04:33:08.986 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:33:14.000 [info] > git config --get commit.template [4ms]
2025-06-14 04:33:14.001 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:33:14.011 [info] > git status -z -uall [5ms]
2025-06-14 04:33:14.012 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:33:19.033 [info] > git config --get commit.template [8ms]
2025-06-14 04:33:19.034 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:33:19.044 [info] > git status -z -uall [4ms]
2025-06-14 04:33:19.046 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:33:24.056 [info] > git config --get commit.template [2ms]
2025-06-14 04:33:24.063 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:33:24.075 [info] > git status -z -uall [5ms]
2025-06-14 04:33:24.076 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
