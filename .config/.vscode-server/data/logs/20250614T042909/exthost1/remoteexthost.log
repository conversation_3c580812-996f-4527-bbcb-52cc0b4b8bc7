2025-06-14 04:29:13.658 [info] Extension host with pid 937 started
2025-06-14 04:29:13.658 [error] Error: EEXIST: file already exists, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/vscode.lock'
2025-06-14 04:29:13.658 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/vscode.lock': Could not acquire lock, checking if the file is stale.
2025-06-14 04:29:13.664 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/vscode.lock': The pid 27916 appears to be gone.
2025-06-14 04:29:13.664 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/vscode.lock': Deleting a stale lock.
2025-06-14 04:29:13.679 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/vscode.lock': Lock acquired.
2025-06-14 04:29:14.269 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-14 04:29:14.270 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-06-14 04:29:14.270 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-06-14 04:29:14.271 [info] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:typescript'
2025-06-14 04:29:15.273 [info] ExtensionService#_doActivateExtension vscode.npm, startup: true, activationEvent: 'workspaceContains:package.json'
2025-06-14 04:29:16.001 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-06-14 04:29:16.002 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-06-14 04:29:16.503 [info] Eager extensions activated
2025-06-14 04:29:16.503 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-14 04:29:16.504 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-14 04:29:16.504 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-06-14 04:29:16.505 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-06-14 04:29:16.505 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onStartupFinished'
2025-06-14 04:29:22.268 [info] ExtensionService#_doActivateExtension GitHub.vscode-pull-request-github, startup: false, activationEvent: 'onStartupFinished'
2025-06-14 04:29:23.663 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-14 04:29:23.663 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-14 04:29:23.663 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:json'
2025-06-14 04:29:26.116 [error] HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2955:23152
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at C1.execute (/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2907:21162)
2025-06-14 04:29:29.523 [error] CodeExpectedError: cannot open vscode-userdata:/Users/<USER>/Library/Application%20Support/Code/User/keybindings.json. Detail: Unable to read file 'vscode-userdata:/Users/<USER>/Library/Application Support/Code/User/keybindings.json' (Error: Unable to resolve nonexistent file 'vscode-userdata:/Users/<USER>/Library/Application Support/Code/User/keybindings.json')
    at Pze.$tryOpenDocument (vscode-file://vscode-app/Applications/Visual%20Studio%20Code.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1283:8666)
2025-06-14 04:29:30.055 [info] ExtensionService#_doActivateExtension vscode.markdown-language-features, startup: false, activationEvent: 'onLanguage:markdown'
2025-06-14 04:33:24.185 [info] Extension host terminating: received terminate message from renderer
2025-06-14 04:33:24.187 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/vscode.lock': Marking the lockfile as scheduled to be released in 6000 ms.
2025-06-14 04:33:24.279 [info] Extension host with pid 937 exiting with code 0
