2025-06-14 04:33:33.506 [info] [main] Log level: Info
2025-06-14 04:33:33.506 [info] [main] Validating found git in: "git"
2025-06-14 04:33:33.506 [info] [main] Using git "2.47.2" from "git"
2025-06-14 04:33:33.506 [info] [Model][doInitialScan] Initial repository scan started
2025-06-14 04:33:33.506 [info] > git rev-parse --show-toplevel [4ms]
2025-06-14 04:33:33.506 [info] > git rev-parse --git-dir --git-common-dir [2937ms]
2025-06-14 04:33:33.506 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-14 04:33:33.506 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-14 04:33:33.506 [info] > git rev-parse --show-toplevel [10ms]
2025-06-14 04:33:33.506 [info] > git config --get commit.template [15ms]
2025-06-14 04:33:33.506 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [136ms]
2025-06-14 04:33:33.506 [info] > git rev-parse --show-toplevel [132ms]
2025-06-14 04:33:33.560 [info] > git status -z -uall [6ms]
2025-06-14 04:33:33.560 [info] > git rev-parse --show-toplevel [48ms]
2025-06-14 04:33:33.566 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-14 04:33:34.359 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [777ms]
2025-06-14 04:33:34.366 [info] > git rev-parse --show-toplevel [802ms]
2025-06-14 04:33:34.389 [info] > git check-ignore -v -z --stdin [17ms]
2025-06-14 04:33:34.390 [info] > git config --get --local branch.main.vscode-merge-base [24ms]
2025-06-14 04:33:34.395 [info] > git config --get commit.template [267ms]
2025-06-14 04:33:34.398 [info] > git rev-parse --show-toplevel [22ms]
2025-06-14 04:33:34.406 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [12ms]
2025-06-14 04:33:34.412 [info] > git rev-parse --show-toplevel [6ms]
2025-06-14 04:33:34.413 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-14 04:33:34.416 [info] > git merge-base refs/heads/main refs/remotes/origin/main [6ms]
2025-06-14 04:33:34.458 [info] > git rev-parse --show-toplevel [42ms]
2025-06-14 04:33:34.472 [info] > git diff --name-status -z --diff-filter=ADMR a9760fde6fc081c302411d62a5d4c72b5a1424fd...refs/remotes/origin/main [52ms]
2025-06-14 04:33:34.485 [info] > git status -z -uall [6ms]
2025-06-14 04:33:34.485 [info] > git rev-parse --show-toplevel [20ms]
2025-06-14 04:33:34.491 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-14 04:33:34.494 [info] > git rev-parse --show-toplevel [4ms]
2025-06-14 04:33:34.501 [info] > git rev-parse --show-toplevel [2ms]
2025-06-14 04:33:34.503 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-14 04:33:34.623 [info] > git show --textconv :server/csvLogger.ts [12ms]
2025-06-14 04:33:34.624 [info] > git ls-files --stage -- server/csvLogger.ts [10ms]
2025-06-14 04:33:34.649 [info] > git cat-file -s 1fce6f51d7d389e752a39c0b2b781e52270f5632 [18ms]
2025-06-14 04:33:35.788 [info] > git config --get commit.template [6ms]
2025-06-14 04:33:35.790 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 04:33:35.805 [info] > git status -z -uall [5ms]
2025-06-14 04:33:35.806 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:33:37.313 [info] > git config --get --local branch.main.github-pr-owner-number [149ms]
2025-06-14 04:33:37.313 [warning] [Git][config] git config failed: Failed to execute git
2025-06-14 04:33:47.996 [info] > git fetch [248ms]
2025-06-14 04:33:47.997 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/eddie333016/BeamTechLandingPage/'
2025-06-14 04:33:48.009 [info] > git config --get commit.template [7ms]
2025-06-14 04:33:48.010 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:33:48.024 [info] > git status -z -uall [8ms]
2025-06-14 04:33:48.025 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 04:33:48.045 [info] > git config --get commit.template [7ms]
2025-06-14 04:33:48.046 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:33:48.071 [info] > git status -z -uall [12ms]
2025-06-14 04:33:48.071 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:33:49.410 [info] > git ls-files --stage -- server/csvLogger.ts [2ms]
2025-06-14 04:33:49.415 [info] > git cat-file -s 1fce6f51d7d389e752a39c0b2b781e52270f5632 [2ms]
2025-06-14 04:33:49.647 [info] > git show --textconv :server/csvLogger.ts [1ms]
2025-06-14 04:33:53.092 [info] > git config --get commit.template [8ms]
2025-06-14 04:33:53.093 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:33:53.107 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:33:53.109 [info] > git status -z -uall [8ms]
2025-06-14 04:33:58.121 [info] > git config --get commit.template [4ms]
2025-06-14 04:33:58.122 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:33:58.130 [info] > git status -z -uall [4ms]
2025-06-14 04:33:58.131 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:34:03.144 [info] > git config --get commit.template [5ms]
2025-06-14 04:34:03.145 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:34:03.153 [info] > git status -z -uall [4ms]
2025-06-14 04:34:03.154 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:34:08.835 [info] > git config --get commit.template [5ms]
2025-06-14 04:34:08.836 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:34:08.845 [info] > git status -z -uall [4ms]
2025-06-14 04:34:08.846 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:34:13.862 [info] > git config --get commit.template [6ms]
2025-06-14 04:34:13.863 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:34:13.873 [info] > git status -z -uall [6ms]
2025-06-14 04:34:13.873 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:34:18.886 [info] > git config --get commit.template [4ms]
2025-06-14 04:34:18.888 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:34:18.896 [info] > git status -z -uall [5ms]
2025-06-14 04:34:18.897 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:34:23.910 [info] > git config --get commit.template [4ms]
2025-06-14 04:34:23.911 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:34:23.918 [info] > git status -z -uall [4ms]
2025-06-14 04:34:23.920 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:34:28.930 [info] > git config --get commit.template [3ms]
2025-06-14 04:34:28.936 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:34:28.944 [info] > git status -z -uall [4ms]
2025-06-14 04:34:28.945 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:34:33.957 [info] > git config --get commit.template [4ms]
2025-06-14 04:34:33.958 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:34:33.966 [info] > git status -z -uall [5ms]
2025-06-14 04:34:33.966 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:34:38.979 [info] > git config --get commit.template [5ms]
2025-06-14 04:34:38.980 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:34:38.987 [info] > git status -z -uall [3ms]
2025-06-14 04:34:38.989 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:34:43.998 [info] > git config --get commit.template [1ms]
2025-06-14 04:34:44.004 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:34:44.013 [info] > git status -z -uall [4ms]
2025-06-14 04:34:44.014 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:36:45.680 [info] > git config --get commit.template [4ms]
2025-06-14 04:36:45.681 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:36:45.689 [info] > git status -z -uall [5ms]
2025-06-14 04:36:45.689 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:36:50.701 [info] > git config --get commit.template [4ms]
2025-06-14 04:36:50.702 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:36:50.711 [info] > git status -z -uall [5ms]
2025-06-14 04:36:50.711 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:36:51.656 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-14 04:36:53.460 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-14 04:36:55.733 [info] > git config --get commit.template [6ms]
2025-06-14 04:36:55.735 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-14 04:36:55.750 [info] > git status -z -uall [10ms]
2025-06-14 04:36:55.750 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:37:00.763 [info] > git config --get commit.template [4ms]
2025-06-14 04:37:00.764 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:37:00.773 [info] > git status -z -uall [5ms]
2025-06-14 04:37:00.773 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:37:05.788 [info] > git config --get commit.template [6ms]
2025-06-14 04:37:05.789 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:37:05.803 [info] > git status -z -uall [7ms]
2025-06-14 04:37:05.803 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 04:37:10.861 [info] > git config --get commit.template [5ms]
2025-06-14 04:37:10.862 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:37:10.870 [info] > git status -z -uall [4ms]
2025-06-14 04:37:10.871 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:37:15.884 [info] > git config --get commit.template [5ms]
2025-06-14 04:37:15.885 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:37:15.895 [info] > git status -z -uall [6ms]
2025-06-14 04:37:15.896 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:37:20.907 [info] > git config --get commit.template [4ms]
2025-06-14 04:37:20.908 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:37:20.918 [info] > git status -z -uall [5ms]
2025-06-14 04:37:20.918 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:37:25.940 [info] > git config --get commit.template [6ms]
2025-06-14 04:37:25.941 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:37:25.948 [info] > git status -z -uall [3ms]
2025-06-14 04:37:25.949 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:37:30.962 [info] > git config --get commit.template [4ms]
2025-06-14 04:37:30.963 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:37:30.970 [info] > git status -z -uall [4ms]
2025-06-14 04:37:30.972 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:37:35.981 [info] > git config --get commit.template [1ms]
2025-06-14 04:37:35.986 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:37:35.993 [info] > git status -z -uall [3ms]
2025-06-14 04:37:35.995 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:37:52.034 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 04:37:52.035 [info] > git config --get commit.template [10ms]
2025-06-14 04:37:52.048 [info] > git status -z -uall [7ms]
2025-06-14 04:37:52.049 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:37:57.060 [info] > git config --get commit.template [3ms]
2025-06-14 04:37:57.062 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:37:57.070 [info] > git status -z -uall [5ms]
2025-06-14 04:37:57.070 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:38:02.084 [info] > git config --get commit.template [4ms]
2025-06-14 04:38:02.085 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:38:02.094 [info] > git status -z -uall [5ms]
2025-06-14 04:38:02.096 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:38:09.828 [info] > git config --get commit.template [2ms]
2025-06-14 04:38:09.834 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:38:09.843 [info] > git status -z -uall [5ms]
2025-06-14 04:38:09.843 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:38:14.858 [info] > git config --get commit.template [4ms]
2025-06-14 04:38:14.860 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:38:14.869 [info] > git status -z -uall [6ms]
2025-06-14 04:38:14.869 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:38:19.887 [info] > git config --get commit.template [7ms]
2025-06-14 04:38:19.888 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:38:19.898 [info] > git status -z -uall [5ms]
2025-06-14 04:38:19.900 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:38:24.915 [info] > git config --get commit.template [5ms]
2025-06-14 04:38:24.917 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:38:24.924 [info] > git status -z -uall [3ms]
2025-06-14 04:38:24.925 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:38:29.940 [info] > git config --get commit.template [4ms]
2025-06-14 04:38:29.941 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:38:29.949 [info] > git status -z -uall [4ms]
2025-06-14 04:38:29.950 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:38:34.962 [info] > git config --get commit.template [2ms]
2025-06-14 04:38:34.967 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:38:34.974 [info] > git status -z -uall [4ms]
2025-06-14 04:38:34.975 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:38:39.989 [info] > git config --get commit.template [5ms]
2025-06-14 04:38:39.990 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:38:39.999 [info] > git status -z -uall [4ms]
2025-06-14 04:38:40.000 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:38:45.014 [info] > git config --get commit.template [4ms]
2025-06-14 04:38:45.016 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:38:45.025 [info] > git status -z -uall [5ms]
2025-06-14 04:38:45.026 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:38:50.038 [info] > git config --get commit.template [5ms]
2025-06-14 04:38:50.040 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:38:50.047 [info] > git status -z -uall [4ms]
2025-06-14 04:38:50.048 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:38:55.061 [info] > git config --get commit.template [4ms]
2025-06-14 04:38:55.062 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:38:55.070 [info] > git status -z -uall [4ms]
2025-06-14 04:38:55.071 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:42:05.698 [info] > git config --get commit.template [4ms]
2025-06-14 04:42:05.699 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:42:05.707 [info] > git status -z -uall [4ms]
2025-06-14 04:42:05.708 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:42:10.723 [info] > git config --get commit.template [4ms]
2025-06-14 04:42:10.724 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:42:10.731 [info] > git status -z -uall [4ms]
2025-06-14 04:42:10.732 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:42:15.747 [info] > git config --get commit.template [6ms]
2025-06-14 04:42:15.750 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-14 04:42:15.762 [info] > git status -z -uall [6ms]
2025-06-14 04:42:15.763 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 04:42:20.778 [info] > git config --get commit.template [5ms]
2025-06-14 04:42:20.780 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 04:42:20.788 [info] > git status -z -uall [4ms]
2025-06-14 04:42:20.789 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:42:25.802 [info] > git config --get commit.template [5ms]
2025-06-14 04:42:25.803 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:42:25.811 [info] > git status -z -uall [4ms]
2025-06-14 04:42:25.812 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:44:18.644 [info] > git config --get commit.template [6ms]
2025-06-14 04:44:18.645 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:44:18.653 [info] > git status -z -uall [4ms]
2025-06-14 04:44:18.654 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:44:19.665 [info] > git ls-files --stage -- test-logging.js [2ms]
2025-06-14 04:44:19.671 [info] > git show --textconv :test-logging.js [17ms]
2025-06-14 04:44:19.672 [info] > git cat-file -s 7113ec72fc0d4ab1fb6a35b3080379a65ee0a523 [2ms]
2025-06-14 04:44:20.124 [info] > git show --textconv :test-logging.js [12ms]
2025-06-14 04:44:20.124 [info] > git ls-files --stage -- test-logging.js [1ms]
2025-06-14 04:44:20.132 [info] > git cat-file -s 7113ec72fc0d4ab1fb6a35b3080379a65ee0a523 [2ms]
2025-06-14 04:44:20.586 [info] > git show --textconv :test-api.js [8ms]
2025-06-14 04:44:20.587 [info] > git ls-files --stage -- test-api.js [2ms]
2025-06-14 04:44:20.597 [info] > git cat-file -s 1d47feb180bf4755dad0dea7442e3f4f88d4e826 [2ms]
2025-06-14 04:44:21.947 [info] > git show --textconv :server/test-logging.ts [8ms]
2025-06-14 04:44:21.948 [info] > git ls-files --stage -- server/test-logging.ts [2ms]
2025-06-14 04:44:21.956 [info] > git cat-file -s 9855137298842a42e4f3c06fd3a5dee5c65a4c59 [2ms]
2025-06-14 04:44:23.044 [info] > git show --textconv :server/routes.ts [5ms]
2025-06-14 04:44:23.045 [info] > git ls-files --stage -- server/routes.ts [2ms]
2025-06-14 04:44:23.050 [info] > git cat-file -s 5e4b8568d273f5b709870ccbfff57a0379d523ca [1ms]
2025-06-14 04:44:23.668 [info] > git config --get commit.template [4ms]
2025-06-14 04:44:23.669 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:44:23.677 [info] > git status -z -uall [4ms]
2025-06-14 04:44:23.678 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:44:28.455 [info] > git show --textconv :augment-guidelines.md [4ms]
2025-06-14 04:44:28.456 [info] > git ls-files --stage -- augment-guidelines.md [1ms]
2025-06-14 04:44:28.461 [info] > git cat-file -s 607d4d97881269e34626106d2b968508237f0c1c [2ms]
2025-06-14 04:44:29.189 [info] > git blame --root --incremental d73e689256f649a54180c8e70bf7d30d8caba561 -- augment-guidelines.md [29ms]
2025-06-14 04:44:34.926 [info] > git config --get commit.template [4ms]
2025-06-14 04:44:34.928 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:44:34.940 [info] > git status -z -uall [8ms]
2025-06-14 04:44:34.940 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-14 04:44:39.953 [info] > git config --get commit.template [4ms]
2025-06-14 04:44:39.954 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:44:39.961 [info] > git status -z -uall [4ms]
2025-06-14 04:44:39.962 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:45:06.668 [info] > git config --get commit.template [5ms]
2025-06-14 04:45:06.670 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:45:06.677 [info] > git status -z -uall [3ms]
2025-06-14 04:45:06.678 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:45:11.690 [info] > git config --get commit.template [4ms]
2025-06-14 04:45:11.692 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:45:11.699 [info] > git status -z -uall [3ms]
2025-06-14 04:45:11.700 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:45:16.713 [info] > git config --get commit.template [4ms]
2025-06-14 04:45:16.715 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:45:16.722 [info] > git status -z -uall [4ms]
2025-06-14 04:45:16.723 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:45:21.735 [info] > git config --get commit.template [4ms]
2025-06-14 04:45:21.736 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:45:21.748 [info] > git status -z -uall [8ms]
2025-06-14 04:45:21.752 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-14 04:46:29.097 [info] > git config --get commit.template [7ms]
2025-06-14 04:46:29.097 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:46:29.107 [info] > git status -z -uall [5ms]
2025-06-14 04:46:29.108 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:46:34.121 [info] > git config --get commit.template [4ms]
2025-06-14 04:46:34.123 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:46:34.131 [info] > git status -z -uall [5ms]
2025-06-14 04:46:34.133 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 04:47:13.027 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-14 04:47:13.027 [info] > git config --get commit.template [10ms]
2025-06-14 04:47:13.036 [info] > git status -z -uall [4ms]
2025-06-14 04:47:13.037 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:47:18.050 [info] > git config --get commit.template [4ms]
2025-06-14 04:47:18.051 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:47:18.059 [info] > git status -z -uall [4ms]
2025-06-14 04:47:18.060 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:47:23.072 [info] > git config --get commit.template [5ms]
2025-06-14 04:47:23.073 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:47:23.080 [info] > git status -z -uall [4ms]
2025-06-14 04:47:23.082 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:47:28.093 [info] > git config --get commit.template [4ms]
2025-06-14 04:47:28.094 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:47:28.101 [info] > git status -z -uall [3ms]
2025-06-14 04:47:28.102 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:47:33.121 [info] > git config --get commit.template [6ms]
2025-06-14 04:47:33.122 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:47:33.133 [info] > git status -z -uall [5ms]
2025-06-14 04:47:33.134 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:47:42.260 [info] > git config --get commit.template [4ms]
2025-06-14 04:47:42.261 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:47:42.269 [info] > git status -z -uall [4ms]
2025-06-14 04:47:42.271 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 05:09:24.094 [info] > git config --get commit.template [6ms]
2025-06-14 05:09:24.095 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 05:09:24.109 [info] > git status -z -uall [9ms]
2025-06-14 05:09:24.109 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 05:10:41.780 [info] > git config --get commit.template [2ms]
2025-06-14 05:10:41.786 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 05:10:41.795 [info] > git status -z -uall [5ms]
2025-06-14 05:10:41.796 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 05:10:41.936 [info] > git merge-base refs/heads/main refs/remotes/origin/main [124ms]
2025-06-14 05:10:41.943 [info] > git blame --root --incremental bbb705910ba68510e8a4e0b555543b16fcff9fad -- augment-guidelines.md [136ms]
2025-06-14 05:10:41.944 [info] > git diff --name-status -z --diff-filter=ADMR bbb705910ba68510e8a4e0b555543b16fcff9fad...refs/remotes/origin/main [1ms]
2025-06-14 05:10:41.971 [info] > git ls-files --stage -- augment-guidelines.md [1ms]
2025-06-14 05:10:41.977 [info] > git cat-file -s 410009172a37194cda1a82f787567da122c04e95 [1ms]
2025-06-14 05:10:42.139 [info] > git config --get --local branch.main.github-pr-owner-number [3ms]
2025-06-14 05:10:42.139 [warning] [Git][config] git config failed: Failed to execute git
2025-06-14 05:10:42.187 [info] > git show --textconv :augment-guidelines.md [1ms]
2025-06-14 05:11:58.581 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-14 05:11:58.582 [info] > git config --get commit.template [14ms]
2025-06-14 05:11:58.606 [info] > git status -z -uall [11ms]
2025-06-14 05:11:58.607 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 05:11:58.814 [info] > git merge-base refs/heads/main refs/remotes/origin/main [179ms]
2025-06-14 05:11:58.822 [info] > git blame --root --incremental fca93b858e9ce1674e24df315ae1b3dd5c614c0a -- augment-guidelines.md [196ms]
2025-06-14 05:11:58.823 [info] > git ls-files --stage -- augment-guidelines.md [10ms]
2025-06-14 05:11:58.833 [info] > git diff --name-status -z --diff-filter=ADMR bbb705910ba68510e8a4e0b555543b16fcff9fad...refs/remotes/origin/main [11ms]
2025-06-14 05:11:58.834 [info] > git cat-file -s 410009172a37194cda1a82f787567da122c04e95 [3ms]
2025-06-14 05:11:59.070 [info] > git show --textconv :augment-guidelines.md [2ms]
2025-06-14 05:12:03.646 [info] > git config --get commit.template [4ms]
2025-06-14 05:12:03.648 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 05:12:03.655 [info] > git status -z -uall [4ms]
2025-06-14 05:12:03.656 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 05:12:08.673 [info] > git config --get commit.template [6ms]
2025-06-14 05:12:08.674 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 05:12:08.688 [info] > git status -z -uall [5ms]
2025-06-14 05:12:08.689 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 05:12:13.703 [info] > git config --get commit.template [5ms]
2025-06-14 05:12:13.705 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 05:12:13.714 [info] > git status -z -uall [5ms]
2025-06-14 05:12:13.714 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 05:12:19.256 [info] > git config --get commit.template [3ms]
2025-06-14 05:12:19.262 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 05:12:19.270 [info] > git status -z -uall [4ms]
2025-06-14 05:12:19.271 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 05:12:24.287 [info] > git config --get commit.template [6ms]
2025-06-14 05:12:24.288 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 05:12:24.296 [info] > git status -z -uall [3ms]
2025-06-14 05:12:24.298 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 05:12:30.257 [info] > git config --get commit.template [9ms]
2025-06-14 05:12:30.259 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-14 05:12:30.277 [info] > git status -z -uall [7ms]
2025-06-14 05:12:30.279 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 05:12:35.301 [info] > git config --get commit.template [9ms]
2025-06-14 05:12:35.303 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 05:12:35.322 [info] > git status -z -uall [10ms]
2025-06-14 05:12:35.323 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 05:12:40.343 [info] > git config --get commit.template [9ms]
2025-06-14 05:12:40.343 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-14 05:12:40.357 [info] > git status -z -uall [7ms]
2025-06-14 05:12:40.358 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 05:12:42.013 [info] > git config --get --local branch.main.github-pr-owner-number [1ms]
2025-06-14 05:12:42.013 [warning] [Git][config] git config failed: Failed to execute git
2025-06-14 05:12:45.372 [info] > git config --get commit.template [5ms]
2025-06-14 05:12:45.374 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 05:12:45.384 [info] > git status -z -uall [5ms]
2025-06-14 05:12:45.386 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 05:12:50.400 [info] > git config --get commit.template [6ms]
2025-06-14 05:12:50.402 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 05:12:50.411 [info] > git status -z -uall [5ms]
2025-06-14 05:12:50.412 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 05:12:55.426 [info] > git config --get commit.template [1ms]
2025-06-14 05:12:55.436 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-14 05:12:55.451 [info] > git status -z -uall [7ms]
2025-06-14 05:12:55.452 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 05:13:00.476 [info] > git config --get commit.template [12ms]
2025-06-14 05:13:00.483 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-14 05:13:00.511 [info] > git status -z -uall [18ms]
2025-06-14 05:13:00.512 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-14 05:13:05.524 [info] > git config --get commit.template [4ms]
2025-06-14 05:13:05.525 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 05:13:05.534 [info] > git status -z -uall [5ms]
2025-06-14 05:13:05.534 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
