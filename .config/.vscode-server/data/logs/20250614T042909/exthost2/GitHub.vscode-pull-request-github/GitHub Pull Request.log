2025-06-14 04:33:35.116 [warning] /home/<USER>/.ssh/config: ENOENT: no such file or directory, open '/home/<USER>/.ssh/config'
2025-06-14 04:33:35.116 [info] [Activation] Extension version: 0.111.**********
2025-06-14 04:33:36.176 [info] [Authentication] Creating hub for .com
2025-06-14 04:33:36.769 [info] [Activation] Looking for git repository
2025-06-14 04:33:36.769 [info] [Activation] Found 0 repositories during activation
2025-06-14 04:33:36.770 [info] [Activation] Git repository found, initializing review manager and pr tree view.
2025-06-14 04:33:36.774 [info] [GitAPI] Registering git provider
2025-06-14 04:33:36.774 [info] [Review+0] Validate state in progress
2025-06-14 04:33:36.774 [info] [Review+0] Validating state...
2025-06-14 04:33:37.049 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-14 04:33:37.155 [warning] Unable to resolve remote: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-14 04:33:37.155 [info] [FolderRepositoryManager+0] Missing upstream check failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-14 04:33:37.157 [info] [FolderRepositoryManager+0] Trying to use globalState for assignableUsers.
2025-06-14 04:33:37.164 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-14 04:33:37.170 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-14 04:33:37.173 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-14 04:33:37.313 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-06-14 04:33:37.538 [error] [GitHubRepository+0] Error querying GraphQL API (MaxIssue): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-14 04:33:37.538 [error] [GitHubRepository+0] Unable to fetch issues with query: Error: GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.
2025-06-14 04:33:37.790 [info] [FolderRepositoryManager+0] Using globalState assignableUsers for 1.
2025-06-14 04:33:38.067 [error] [GitHubRepository+0] Error querying GraphQL API (GetSuggestedActors): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-14 04:33:48.027 [info] [Activation] Repo state for file:///home/<USER>/workspace changed.
2025-06-14 04:33:48.027 [info] [Activation] Repo file:///home/<USER>/workspace has already been setup.
2025-06-14 04:35:36.774 [error] [Review+0] Timeout occurred while validating state.
2025-06-14 05:10:41.807 [info] [Review+0] Queuing additional validate state
2025-06-14 05:10:41.807 [info] [Review+0] Validating state...
2025-06-14 05:10:41.813 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-14 05:10:41.816 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-14 05:10:41.817 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-14 05:10:42.126 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-14 05:10:42.127 [warning] Unable to resolve remote: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-14 05:10:42.128 [info] [FolderRepositoryManager+0] Using in-memory cached assignable users.
2025-06-14 05:10:42.140 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-06-14 05:10:42.151 [error] [GitHubRepository+0] Error querying GraphQL API (MaxIssue): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-14 05:10:42.152 [error] [GitHubRepository+0] Unable to fetch issues with query: Error: GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.
2025-06-14 05:11:58.626 [info] [Review+0] Queuing additional validate state
2025-06-14 05:11:58.636 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-14 05:11:58.637 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-14 05:11:58.637 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-14 05:11:59.034 [error] [GitHubRepository+0] Error querying GraphQL API (MaxIssue): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-14 05:11:59.034 [error] [GitHubRepository+0] Unable to fetch issues with query: Error: GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.
2025-06-14 05:12:41.808 [error] [Review+0] Timeout occurred while validating state.
2025-06-14 05:12:41.808 [info] [Review+0] Validating state...
2025-06-14 05:12:42.000 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-14 05:12:42.000 [warning] Unable to resolve remote: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-14 05:12:42.001 [info] [FolderRepositoryManager+0] Using in-memory cached assignable users.
2025-06-14 05:12:42.013 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-06-14 05:14:41.809 [error] [Review+0] Timeout occurred while validating state.
