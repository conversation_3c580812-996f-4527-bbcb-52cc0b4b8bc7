<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1" />
    <title>beam.tech - Australian Web Solutions</title>

    <!-- Botpress Chatbot Integration -->
    <script src="https://cdn.botpress.cloud/webchat/v3.0/inject.js"></script>
    <script>
      // Wait for Botpress to be available before initializing
      (function() {
        function initBotpress() {
          if (window.botpress) {
            window.botpress.init({
              "botId": "8b4fa29e-1545-4fde-9991-8be5f36a824c",
              "configuration": {
                "version": "v1",
                "website": {
                  "title": "https://beamtradingx.com/",
                  "link": "https://beamtradingx.com/"
                },
                "email": {
                  "title": "<EMAIL>",
                  "link": "<EMAIL>"
                },
                "phone": {},
                "termsOfService": {
                  "title": "Terms of service",
                  "link": "https://beamtradingx.com/"
                },
                "privacyPolicy": {
                  "title": "Privacy policy",
                  "link": "https://beamtradingx.com/"
                },
                "color": "#9333ea",
                "variant": "solid",
                "headerVariant": "solid",
                "themeMode": "light",
                "fontFamily": "inter",
                "radius": 4,
                "feedbackEnabled": false,
                "footer": "[⚡ by Botpress](https://botpress.com/?from=webchat)",
                "additionalStylesheetUrl": "https://files.bpcontent.cloud/2025/06/14/04/20250614044255-FAH0974Q.css"
              },
              "clientId": "58988b78-12d9-40bd-aa18-305bed1f1fa7"
            });
          } else {
            // Retry after 100ms if Botpress isn't loaded yet
            setTimeout(initBotpress, 100);
          }
        }

        // Start initialization when DOM is ready
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', initBotpress);
        } else {
          initBotpress();
        }
      })();
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <!-- This is a replit script which adds a banner on the top of the page when opened in development mode outside the replit environment -->
    <script type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js"></script>
  </body>
</html>