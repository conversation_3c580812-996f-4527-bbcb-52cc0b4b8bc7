import React from 'react';
import { Globe } from 'lucide-react';

const TechStack: React.FC = () => {

  return (
    <section id="tech" className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Our <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">Technology Stack</span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            We leverage cutting-edge technologies to build scalable, performant, and future-ready solutions
          </p>
        </div>

        {/* Technology Categories */}
        <div className="grid lg:grid-cols-2 gap-8 mb-16">
          {categories.map((category) => {
            const IconComponent = category.icon;
            return (
              <div
                key={category.id}
                className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-gray-700"
              >
                {/* Category Header */}
                <div className="flex items-center space-x-4 mb-6">
                  <div className={`p-3 bg-gradient-to-r ${category.color} rounded-xl`}>
                    <IconComponent className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                    {category.title}
                  </h3>
                </div>

                {/* Technologies Grid */}
                <div className="grid grid-cols-2 gap-4">
                  {category.technologies.map((tech, index) => (
                    <div
                      key={index}
                      className="group p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200"
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`w-2 h-2 bg-gradient-to-r ${category.color} rounded-full mt-2 flex-shrink-0`}></div>
                        <div>
                          <h4 className="font-semibold text-gray-900 dark:text-white text-sm">
                            {tech.name}
                          </h4>
                          <p className="text-xs text-gray-600 dark:text-gray-300 mt-1">
                            {tech.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>

        {/* Australian Service Guarantees */}
        <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-8 text-white">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 text-center">
            <div>
              <div className="text-2xl font-bold mb-2">40+ Years</div>
              <div className="text-purple-100">Combined Experience</div>
            </div>
            <div>
              <div className="text-2xl font-bold mb-2">Australian</div>
              <div className="text-purple-100">Data Centres Only</div>
            </div>
            <div>
              <div className="text-2xl font-bold mb-2">24/7</div>
              <div className="text-purple-100">Australian Support</div>
            </div>
            <div>
              <div className="text-2xl font-bold mb-2">No Hidden</div>
              <div className="text-purple-100">Fees—Ever</div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center space-x-2 bg-gray-100 dark:bg-gray-800 rounded-full px-6 py-3 mb-6">
            <Globe className="h-5 w-5 text-purple-600 dark:text-purple-400" />
            <span className="text-gray-700 dark:text-gray-300 font-medium">Always Learning, Always Innovating</span>
          </div>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Our team stays at the forefront of technology, continuously learning and adopting new tools to deliver the best solutions for our clients.
          </p>
        </div>
      </div>
    </section>
  );
};

export default TechStack;