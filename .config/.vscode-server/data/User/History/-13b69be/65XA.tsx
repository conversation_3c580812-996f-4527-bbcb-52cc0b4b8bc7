import React from 'react';
import Header from './components/Header';
import Hero from './components/Hero';
import Services from './components/Services';
import Portfolio from './components/Portfolio';
import TechStack from './components/TechStack';
import Testimonials from './components/Testimonials';
import Contact from './components/Contact';
import Footer from './components/Footer';
import ChatBubble from './components/ChatBubble';

function App() {
  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300">
      <Header />
      <main>
        <Hero />
        <Services />
        <Portfolio />
        <TechStack />
        <Testimonials />
        <Contact />
      </main>
      <Footer />

      {/* Chat Bubble - appears on all pages */}
      <ChatBubble />
    </div>
  );
}

export default App;