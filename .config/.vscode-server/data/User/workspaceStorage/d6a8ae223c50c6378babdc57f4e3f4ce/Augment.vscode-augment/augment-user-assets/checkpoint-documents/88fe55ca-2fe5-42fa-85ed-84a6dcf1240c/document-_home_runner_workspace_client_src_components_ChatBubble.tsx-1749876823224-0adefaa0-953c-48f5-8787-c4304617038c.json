{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ChatBubble.tsx"}, "modifiedCode": "import React, { useState } from 'react';\nimport { MessageCircle, X, Send } from 'lucide-react';\n\nconst ChatBubble: React.FC = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [message, setMessage] = useState('');\n\n  const toggleChat = () => {\n    setIsOpen(!isOpen);\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (message.trim()) {\n      // For now, just show an alert. This can be replaced with actual chat functionality\n      alert(`Thanks for your message: \"${message}\". We'll get back to you soon! For immediate assistance, please call +61 8 9757 2000 <NAME_EMAIL>`);\n      setMessage('');\n      setIsOpen(false);\n    }\n  };\n\n  return (\n    <>\n      {/* Chat Bubble */}\n      <div className=\"fixed bottom-6 right-6 z-50\">\n        <button\n          onClick={toggleChat}\n          className=\"bg-gradient-to-r from-purple-600 to-blue-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 focus:outline-none focus:ring-4 focus:ring-purple-300\"\n          aria-label=\"Open chat\"\n        >\n          {isOpen ? (\n            <X className=\"h-6 w-6\" />\n          ) : (\n            <MessageCircle className=\"h-6 w-6\" />\n          )}\n        </button>\n      </div>\n\n      {/* Chat Window */}\n      {isOpen && (\n        <div className=\"fixed bottom-24 right-6 w-80 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 z-50 overflow-hidden\">\n          {/* Header */}\n          <div className=\"bg-gradient-to-r from-purple-600 to-blue-600 text-white p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"font-semibold\">beam.tech Support</h3>\n                <p className=\"text-sm text-purple-100\">We're here to help!</p>\n              </div>\n              <button\n                onClick={toggleChat}\n                className=\"text-white hover:text-purple-200 transition-colors duration-200\"\n                aria-label=\"Close chat\"\n              >\n                <X className=\"h-5 w-5\" />\n              </button>\n            </div>\n          </div>\n\n          {/* Chat Content */}\n          <div className=\"p-4 h-64 overflow-y-auto\">\n            <div className=\"space-y-4\">\n              {/* Welcome Message */}\n              <div className=\"bg-gray-100 dark:bg-gray-700 rounded-lg p-3\">\n                <p className=\"text-sm text-gray-800 dark:text-gray-200\">\n                  👋 Hi there! Welcome to beam.tech. How can we help you today?\n                </p>\n              </div>\n              \n              {/* Quick Actions */}\n              <div className=\"space-y-2\">\n                <p className=\"text-xs text-gray-600 dark:text-gray-400 font-medium\">Quick Actions:</p>\n                <button\n                  onClick={() => {\n                    setMessage('I need a quote for Small Business Essentials');\n                  }}\n                  className=\"w-full text-left text-sm bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 p-2 rounded hover:bg-purple-100 dark:hover:bg-purple-900/40 transition-colors duration-200\"\n                >\n                  💼 Get a quote for Small Business Essentials\n                </button>\n                <button\n                  onClick={() => {\n                    setMessage('I want to schedule a free discovery call');\n                  }}\n                  className=\"w-full text-left text-sm bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 p-2 rounded hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors duration-200\"\n                >\n                  📞 Schedule a free discovery call\n                </button>\n                <button\n                  onClick={() => {\n                    setMessage('I have a question about your services');\n                  }}\n                  className=\"w-full text-left text-sm bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 p-2 rounded hover:bg-green-100 dark:hover:bg-green-900/40 transition-colors duration-200\"\n                >\n                  ❓ Ask about our services\n                </button>\n              </div>\n\n              {/* Contact Info */}\n              <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-xs text-gray-600 dark:text-gray-400\">\n                <p className=\"font-medium mb-1\">Direct Contact:</p>\n                <p>📧 <EMAIL></p>\n                <p>📞 +61 8 9757 2000</p>\n                <p>🕒 24-hour response guarantee</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Message Input */}\n          <form onSubmit={handleSubmit} className=\"p-4 border-t border-gray-200 dark:border-gray-700\">\n            <div className=\"flex space-x-2\">\n              <input\n                type=\"text\"\n                value={message}\n                onChange={(e) => setMessage(e.target.value)}\n                placeholder=\"Type your message...\"\n                className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n              />\n              <button\n                type=\"submit\"\n                disabled={!message.trim()}\n                className=\"bg-gradient-to-r from-purple-600 to-blue-600 text-white p-2 rounded-lg hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\"\n                aria-label=\"Send message\"\n              >\n                <Send className=\"h-4 w-4\" />\n              </button>\n            </div>\n          </form>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default ChatBubble;\n"}