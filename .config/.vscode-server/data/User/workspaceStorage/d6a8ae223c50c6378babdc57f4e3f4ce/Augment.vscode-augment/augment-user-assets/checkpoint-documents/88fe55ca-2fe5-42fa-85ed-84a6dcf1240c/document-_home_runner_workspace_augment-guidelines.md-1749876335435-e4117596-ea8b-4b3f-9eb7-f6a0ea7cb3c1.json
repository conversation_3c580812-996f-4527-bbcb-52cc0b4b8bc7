{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "augment-guidelines.md"}, "originalCode": "# Augment Development Guidelines\n\n## Table of Contents\n1. [Project Overview](#project-overview)\n2. [Code Structure & Organization](#code-structure--organization)\n3. [TypeScript Standards](#typescript-standards)\n4. [React Development Conventions](#react-development-conventions)\n5. [Backend Development Standards](#backend-development-standards)\n6. [Styling & UI Guidelines](#styling--ui-guidelines)\n7. [Database & Schema Management](#database--schema-management)\n8. [Testing Conventions](#testing-conventions)\n9. [Build & Development Workflow](#build--development-workflow)\n10. [Git & Version Control](#git--version-control)\n\n## Project Overview\n\nThis is a modern full-stack TypeScript application built with:\n- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS + shadcn/ui\n- **Backend**: Express.js + TypeScript + Drizzle ORM + PostgreSQL\n- **Architecture**: Monorepo with clear client/server/shared separation\n- **Deployment**: Replit with autoscale configuration\n\n## Code Structure & Organization\n\n### Directory Structure\n```\n/\n├── client/                 # Frontend React application\n│   ├── src/\n│   │   ├── components/     # React components (PascalCase)\n│   │   ├── hooks/          # Custom React hooks (camelCase, use prefix)\n│   │   ├── types/          # TypeScript type definitions\n│   │   └── main.tsx        # Application entry point\n│   └── index.html          # HTML template\n├── server/                 # Backend Express application\n│   ├── index.ts           # Server entry point\n│   ├── routes.ts          # API route definitions\n│   ├── storage.ts         # Database abstraction layer\n│   └── vite.ts            # Development server configuration\n├── shared/                # Shared code between client/server\n│   └── schema.ts          # Database schema & validation\n└── [config files]         # Build and configuration files\n```\n\n### File Naming Conventions\n- **React Components**: PascalCase (e.g., `Header.tsx`, `UserProfile.tsx`)\n- **Hooks**: camelCase with `use` prefix (e.g., `useTheme.tsx`, `useAuth.tsx`)\n- **Utilities**: camelCase (e.g., `apiClient.ts`, `formatDate.ts`)\n- **Types/Interfaces**: PascalCase (e.g., `User.ts`, `ApiResponse.ts`)\n- **Server Files**: camelCase (e.g., `routes.ts`, `storage.ts`)\n\n### Import/Export Standards\n- Use named exports for components and utilities\n- Use default exports only for main component files\n- Group imports: external libraries first, then internal modules\n- Use path aliases: `@/` for client src, `@shared/` for shared code\n\n```typescript\n// ✅ Good import order\nimport React, { useState, useEffect } from 'react';\nimport { Button } from '@radix-ui/react-button';\nimport { useTheme } from '@/hooks/useTheme';\nimport { User } from '@shared/schema';\n\n// ✅ Named exports preferred\nexport const Header: React.FC = () => { ... };\nexport { Header as default };\n```\n\n## TypeScript Standards\n\n### Type Definitions\n- Define interfaces in `types/` directory or co-located with components\n- Use PascalCase for interfaces and types\n- Prefer interfaces over type aliases for object shapes\n- Use strict TypeScript configuration\n\n```typescript\n// ✅ Interface definition\nexport interface User {\n  id: number;\n  username: string;\n  email?: string;\n}\n\n// ✅ Component props interface\ninterface HeaderProps {\n  title: string;\n  onMenuClick?: () => void;\n}\n```\n\n### Type Safety Practices\n- Always type function parameters and return values\n- Use `React.FC` for functional components\n- Leverage TypeScript's strict mode\n- Use Zod for runtime validation of external data\n\n```typescript\n// ✅ Properly typed component\nconst Header: React.FC<HeaderProps> = ({ title, onMenuClick }) => {\n  const [isOpen, setIsOpen] = useState<boolean>(false);\n  \n  const handleClick = (): void => {\n    setIsOpen(!isOpen);\n    onMenuClick?.();\n  };\n\n  return <header>{title}</header>;\n};\n```\n\n## React Development Conventions\n\n### Component Structure\n- Use functional components with hooks\n- Implement proper TypeScript typing\n- Follow single responsibility principle\n- Use descriptive component and prop names\n\n### State Management\n- Use `useState` for local component state\n- Use custom hooks for shared stateful logic\n- Implement proper dependency arrays in `useEffect`\n- Use `useCallback` and `useMemo` for performance optimization when needed\n\n### Form Management\n- Use `useForm` hook for complex form handling with validation\n- Implement proper error states and user feedback\n- Add loading states for async operations\n- Use TypeScript interfaces for form data types\n- **Multi-Step Forms**: Implement step-by-step validation and navigation\n- **Conditional Fields**: Show/hide fields based on user selections\n- **Progress Indicators**: Visual feedback for multi-step processes\n\n```typescript\n// ✅ Custom hook example\nexport const useTheme = () => {\n  const [isDark, setIsDark] = useState<boolean>(false);\n\n  useEffect(() => {\n    const saved = localStorage.getItem('theme');\n    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n    \n    if (saved === 'dark' || (!saved && prefersDark)) {\n      setIsDark(true);\n      document.documentElement.classList.add('dark');\n    }\n  }, []);\n\n  const toggleTheme = useCallback((): void => {\n    setIsDark(prev => {\n      const newValue = !prev;\n      if (newValue) {\n        document.documentElement.classList.add('dark');\n        localStorage.setItem('theme', 'dark');\n      } else {\n        document.documentElement.classList.remove('dark');\n        localStorage.setItem('theme', 'light');\n      }\n      return newValue;\n    });\n  }, []);\n\n  return { isDark, toggleTheme };\n};\n```\n\n### Event Handling\n- Use descriptive handler names with `handle` prefix\n- Implement proper TypeScript event typing\n- Use arrow functions for inline handlers when appropriate\n\n### Utility Functions\n- Create reusable utility functions in `utils/` directory\n- Use proper TypeScript typing for all utilities\n- Implement validation utilities with Zod integration\n- Create navigation utilities for smooth scrolling\n- Build email service abstractions for form submissions\n\n## Backend Development Standards\n\n### API Structure\n- Prefix all API routes with `/api`\n- Use RESTful conventions for route naming\n- Implement proper error handling middleware\n- Use TypeScript for all server code\n- **Endpoint Patterns**: `/api/contact`, `/api/newsletter`, `/api/quick-quote`\n- **Request Validation**: Validate required fields before processing\n- **Response Format**: Consistent JSON responses with success/error states\n\n### Database Layer\n- Use Drizzle ORM for type-safe database operations\n- Implement storage interface abstraction\n- Define schemas in `shared/schema.ts`\n- Use Zod for input validation\n\n```typescript\n// ✅ Storage interface pattern\nexport interface IStorage {\n  getUser(id: number): Promise<User | undefined>;\n  getUserByUsername(username: string): Promise<User | undefined>;\n  createUser(user: InsertUser): Promise<User>;\n}\n\n// ✅ Implementation\nexport class MemStorage implements IStorage {\n  async getUser(id: number): Promise<User | undefined> {\n    return this.users.get(id);\n  }\n}\n```\n\n### Error Handling\n- Implement centralized error handling middleware\n- Use proper HTTP status codes\n- Return consistent error response format\n- Log errors appropriately\n\n## Styling & UI Guidelines\n\n### Tailwind CSS Standards\n- Use Tailwind utility classes for styling\n- Implement dark mode with `dark:` prefix\n- Use CSS variables for theme customization\n- Follow mobile-first responsive design\n\n### Component Styling Patterns\n```typescript\n// ✅ Tailwind class organization\n<button className={`\n  inline-flex items-center justify-center px-8 py-4\n  bg-gradient-to-r from-purple-600 to-blue-600\n  text-white font-semibold rounded-lg shadow-lg\n  hover:shadow-xl transform hover:-translate-y-1\n  transition-all duration-300\n`}>\n```\n\n### shadcn/ui Integration\n- Use shadcn/ui components as base building blocks\n- Customize components through Tailwind classes\n- Maintain consistent component API patterns\n- Follow Radix UI accessibility standards\n\n## Database & Schema Management\n\n### Schema Definition\n- Define all schemas in `shared/schema.ts`\n- Use Drizzle ORM table definitions\n- Implement Zod validation schemas\n- Export TypeScript types from schemas\n\n### Migration Strategy\n- Use Drizzle Kit for schema migrations\n- Run `npm run db:push` for development changes\n- Maintain DATABASE_URL environment variable\n- Use PostgreSQL with Neon serverless driver\n\n## Testing Conventions\n\n### Test Organization\n- Co-locate tests with components when possible\n- Use descriptive test names\n- Follow AAA pattern (Arrange, Act, Assert)\n- Test user interactions, not implementation details\n\n### Testing Tools\n- Use React Testing Library for component tests\n- Implement unit tests for utility functions\n- Use TypeScript in test files\n- Mock external dependencies appropriately\n\n## Build & Development Workflow\n\n### Development Commands\n```bash\nnpm run dev      # Start development server\nnpm run build    # Build for production\nnpm run check    # TypeScript type checking\nnpm run db:push  # Push database schema changes\n```\n\n### Build Configuration\n- Vite for frontend bundling and development\n- esbuild for backend production builds\n- TypeScript compilation with strict mode\n- Environment-specific configurations\n\n### Package Management\n- Use npm for dependency management\n- Keep dependencies up to date\n- Separate devDependencies from runtime dependencies\n- Use exact versions for critical dependencies\n\n## Git & Version Control\n\n### Commit Message Format\n```\ntype(scope): description\n\nExamples:\nfeat(auth): add user authentication\nfix(ui): resolve header navigation issue\ndocs(readme): update installation instructions\nrefactor(api): simplify user service logic\n```\n\n### Branch Strategy\n- Use descriptive branch names\n- Prefix with feature/, fix/, or docs/\n- Keep branches focused and small\n- Delete merged branches\n\n### Code Review Guidelines\n- Review for TypeScript type safety\n- Check for proper error handling\n- Ensure consistent code style\n- Verify responsive design implementation\n- Test functionality across different screen sizes\n\n---\n\n## Service Offerings\n\n### Small Business Essentials\n- **Target Audience**: Small businesses needing essential digital services\n- **Service Categories**:\n  - Cloud hosting setup\n  - Contact & booking forms\n  - Website speed optimization\n  - Bug fixes and maintenance\n  - Email & domain configuration\n- **Quick Quote System**: Streamlined multi-step form for rapid quote generation\n- **Service Delivery**: Focus on affordable, reliable solutions for small business needs\n\n---\n\n## Recent Updates\n\n### Location Information\n- **Company Location**: Margaret River and Canberra, Australia\n- **Footer Text**: \"Made with ❤️ in Margaret River and Canberra, Australia\"\n- **Contact Information**: Updated across Footer and Contact components\n- **Phone Number**: +61 8 9757 2000 (Australian number)\n- **Chamber Membership**: Proud member of the Margaret River Chamber of Commerce\n\n### Interactive Features Implementation\n- **Contact Form**: Full functionality with validation, loading states, and Resend email integration\n- **Newsletter Subscription**: Complete Resend integration with welcome emails and admin notifications\n- **Quick Quote Modal**: Multi-step modal form for Small Business Essentials service with smooth animations\n- **Live Chat**: Botpress chatbot integration with bubble widget and functional \"Start Live Chat\" button\n- **Navigation**: Smooth scrolling to sections with proper anchor handling\n- **CTA Buttons**: All call-to-action buttons now functional and navigate to contact form, quick quote, or live chat\n- **Form Validation**: Real-time validation with error messages and accessibility features\n- **Loading States**: Proper loading indicators for all async operations\n- **Modal System**: Accessible modal with keyboard navigation, backdrop click handling, and focus management\n\n### Email Service Integration (Resend)\n- **Service Provider**: Resend.com - Simple, reliable email API\n- **Contact Form System**: Dual emails (confirmation + notification) with full form data\n- **Newsletter System**: Welcome emails + admin notifications for new subscribers\n- **Domain Integration**: <NAME_EMAIL> for all emails\n- **HTML Templates**: Beautiful responsive email templates with beam.tech branding\n- **Security**: API token stored in environment variables, not client-side\n- **Error Handling**: Comprehensive error handling with user-friendly messages\n- **Production Ready**: All forms tested and working with Message IDs confirmed\n\n### Text File Logging System\n- **Contact Form Logging**: Tracks all submissions in `server/logs/contact-form-submissions.txt`\n- **Newsletter Logging**: Tracks all subscriptions in `server/logs/newsletter-subscriptions.txt`\n- **Simple Format**: Human-readable text format with structured data per line\n- **Timestamp Format**: ISO 8601 timestamps for consistent date/time tracking\n- **Status Tracking**: Logs both successful and failed submissions with detailed debugging\n- **File Management**: Auto-creates log files and directories on server startup\n- **Error Resilience**: Logging failures don't break email functionality\n- **Append-Only**: New entries are appended, existing data is preserved\n- **Production Ready**: Fully tested and working with comprehensive debugging output\n\n### Quick Quote Modal Implementation\n- **Component Structure**: `QuickQuoteModal.tsx` with multi-step form logic\n- **State Management**: Uses `useState` for step navigation and `useForm` for form handling\n- **Validation**: Real-time validation with `validateQuickQuoteForm` function\n- **API Integration**: Submits to `/api/quick-quote` endpoint with full error handling\n- **Email Templates**: Confirmation and notification emails with responsive HTML design\n- **Accessibility**: Keyboard navigation, focus management, and screen reader support\n- **Animation**: Smooth slide-in/fade-in transitions with Tailwind CSS\n- **Mobile Responsive**: Optimized for all screen sizes with proper modal sizing\n\n### Botpress Chatbot Integration\n- **Implementation**: Bubble chat widget integrated site-wide via HTML script tags\n- **Scripts**: Uses Botpress v3.0 webchat injection with custom configuration\n- **Utility Functions**: `client/src/utils/chatbot.ts` provides openChat, closeChat, toggleChat functions\n- **Contact Integration**: \"Start Live Chat\" button in Contact component opens Botpress chat\n- **Global Availability**: Chat bubble appears on all pages without interfering with modals or forms\n- **Browser Compatibility**: Works across modern browsers with fallback error handling\n\n### Component Simplification\n- **TechStack Component**: Simplified by removing technology category cards (Frontend, Backend, AI/ML, Cloud & DevOps)\n- **Focus on Value**: Maintained Australian service guarantees section while reducing technical complexity\n- **Clean Design**: Streamlined layout focuses on business value rather than technical details\n- **Performance**: Reduced component complexity improves page load and rendering performance\n\n### Australian-Focused Value Propositions\n- **Trust Indicators**: \"100% Australian-owned and operated\" prominently displayed\n- **Local Presence**: Margaret River & Canberra locations highlighted throughout\n- **Transparency Promises**: \"Fixed-price quotes. No hidden fees—ever\"\n- **Data Sovereignty**: \"Your data stays onshore: Australian data centres only\"\n- **Service Guarantees**: \"24-hour response time on all support tickets\"\n- **Professional Approach**: \"Free discovery call with a senior engineer, not a salesperson\"\n- **Plain Communication**: \"Plain-English agreements - no nonsense\"\n- **Scope Management**: \"If scope changes, we re-quote—never surprise-bill\"\n- **Experience Claims**: \"Over 40 years of combined web & cloud experience\"\n- **Geographic Coverage**: \"Serving small businesses from the South West to the ACT\"\n\n---\n\n## Quick Reference\n\n### Essential Commands\n- `npm run dev` - Start development server (port 5000)\n- `npm run build` - Production build\n- `npm run check` - Type checking\n- `npm run db:push` - Update database schema\n\n### Testing & Verification\n- **API Testing**: Use curl to test endpoints (`curl -X POST http://localhost:5000/api/quick-quote`)\n- **Form Validation**: Test all form fields with invalid/valid data\n- **Modal Functionality**: Verify modal opens, closes, and handles keyboard navigation\n- **Email Integration**: Check server logs for email sending status\n- **CSV Logging**: Verify log files are created and populated correctly\n- **Responsive Design**: Test on different screen sizes and devices\n\n### Key Patterns\n- Functional components with TypeScript\n- Custom hooks for shared logic\n- Tailwind for styling with dark mode\n- Drizzle ORM for database operations\n- Zod for runtime validation\n\n### File Extensions\n- `.tsx` for React components\n- `.ts` for TypeScript utilities\n- `.css` for global styles (minimal usage)\n", "modifiedCode": "# Augment Development Guidelines\n\n## Table of Contents\n1. [Project Overview](#project-overview)\n2. [Code Structure & Organization](#code-structure--organization)\n3. [TypeScript Standards](#typescript-standards)\n4. [React Development Conventions](#react-development-conventions)\n5. [Backend Development Standards](#backend-development-standards)\n6. [Styling & UI Guidelines](#styling--ui-guidelines)\n7. [Database & Schema Management](#database--schema-management)\n8. [Testing Conventions](#testing-conventions)\n9. [Build & Development Workflow](#build--development-workflow)\n10. [Git & Version Control](#git--version-control)\n\n## Project Overview\n\nThis is a modern full-stack TypeScript application built with:\n- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS + shadcn/ui\n- **Backend**: Express.js + TypeScript + Drizzle ORM + PostgreSQL\n- **Architecture**: Monorepo with clear client/server/shared separation\n- **Deployment**: Replit with autoscale configuration\n\n## Code Structure & Organization\n\n### Directory Structure\n```\n/\n├── client/                 # Frontend React application\n│   ├── src/\n│   │   ├── components/     # React components (PascalCase)\n│   │   ├── hooks/          # Custom React hooks (camelCase, use prefix)\n│   │   ├── types/          # TypeScript type definitions\n│   │   └── main.tsx        # Application entry point\n│   └── index.html          # HTML template\n├── server/                 # Backend Express application\n│   ├── index.ts           # Server entry point\n│   ├── routes.ts          # API route definitions\n│   ├── storage.ts         # Database abstraction layer\n│   └── vite.ts            # Development server configuration\n├── shared/                # Shared code between client/server\n│   └── schema.ts          # Database schema & validation\n└── [config files]         # Build and configuration files\n```\n\n### File Naming Conventions\n- **React Components**: PascalCase (e.g., `Header.tsx`, `UserProfile.tsx`)\n- **Hooks**: camelCase with `use` prefix (e.g., `useTheme.tsx`, `useAuth.tsx`)\n- **Utilities**: camelCase (e.g., `apiClient.ts`, `formatDate.ts`)\n- **Types/Interfaces**: PascalCase (e.g., `User.ts`, `ApiResponse.ts`)\n- **Server Files**: camelCase (e.g., `routes.ts`, `storage.ts`)\n\n### Import/Export Standards\n- Use named exports for components and utilities\n- Use default exports only for main component files\n- Group imports: external libraries first, then internal modules\n- Use path aliases: `@/` for client src, `@shared/` for shared code\n\n```typescript\n// ✅ Good import order\nimport React, { useState, useEffect } from 'react';\nimport { Button } from '@radix-ui/react-button';\nimport { useTheme } from '@/hooks/useTheme';\nimport { User } from '@shared/schema';\n\n// ✅ Named exports preferred\nexport const Header: React.FC = () => { ... };\nexport { Header as default };\n```\n\n## TypeScript Standards\n\n### Type Definitions\n- Define interfaces in `types/` directory or co-located with components\n- Use PascalCase for interfaces and types\n- Prefer interfaces over type aliases for object shapes\n- Use strict TypeScript configuration\n\n```typescript\n// ✅ Interface definition\nexport interface User {\n  id: number;\n  username: string;\n  email?: string;\n}\n\n// ✅ Component props interface\ninterface HeaderProps {\n  title: string;\n  onMenuClick?: () => void;\n}\n```\n\n### Type Safety Practices\n- Always type function parameters and return values\n- Use `React.FC` for functional components\n- Leverage TypeScript's strict mode\n- Use Zod for runtime validation of external data\n\n```typescript\n// ✅ Properly typed component\nconst Header: React.FC<HeaderProps> = ({ title, onMenuClick }) => {\n  const [isOpen, setIsOpen] = useState<boolean>(false);\n  \n  const handleClick = (): void => {\n    setIsOpen(!isOpen);\n    onMenuClick?.();\n  };\n\n  return <header>{title}</header>;\n};\n```\n\n## React Development Conventions\n\n### Component Structure\n- Use functional components with hooks\n- Implement proper TypeScript typing\n- Follow single responsibility principle\n- Use descriptive component and prop names\n\n### State Management\n- Use `useState` for local component state\n- Use custom hooks for shared stateful logic\n- Implement proper dependency arrays in `useEffect`\n- Use `useCallback` and `useMemo` for performance optimization when needed\n\n### Form Management\n- Use `useForm` hook for complex form handling with validation\n- Implement proper error states and user feedback\n- Add loading states for async operations\n- Use TypeScript interfaces for form data types\n- **Multi-Step Forms**: Implement step-by-step validation and navigation\n- **Conditional Fields**: Show/hide fields based on user selections\n- **Progress Indicators**: Visual feedback for multi-step processes\n\n```typescript\n// ✅ Custom hook example\nexport const useTheme = () => {\n  const [isDark, setIsDark] = useState<boolean>(false);\n\n  useEffect(() => {\n    const saved = localStorage.getItem('theme');\n    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n    \n    if (saved === 'dark' || (!saved && prefersDark)) {\n      setIsDark(true);\n      document.documentElement.classList.add('dark');\n    }\n  }, []);\n\n  const toggleTheme = useCallback((): void => {\n    setIsDark(prev => {\n      const newValue = !prev;\n      if (newValue) {\n        document.documentElement.classList.add('dark');\n        localStorage.setItem('theme', 'dark');\n      } else {\n        document.documentElement.classList.remove('dark');\n        localStorage.setItem('theme', 'light');\n      }\n      return newValue;\n    });\n  }, []);\n\n  return { isDark, toggleTheme };\n};\n```\n\n### Event Handling\n- Use descriptive handler names with `handle` prefix\n- Implement proper TypeScript event typing\n- Use arrow functions for inline handlers when appropriate\n\n### Utility Functions\n- Create reusable utility functions in `utils/` directory\n- Use proper TypeScript typing for all utilities\n- Implement validation utilities with Zod integration\n- Create navigation utilities for smooth scrolling\n- Build email service abstractions for form submissions\n\n## Backend Development Standards\n\n### API Structure\n- Prefix all API routes with `/api`\n- Use RESTful conventions for route naming\n- Implement proper error handling middleware\n- Use TypeScript for all server code\n- **Endpoint Patterns**: `/api/contact`, `/api/newsletter`, `/api/quick-quote`\n- **Request Validation**: Validate required fields before processing\n- **Response Format**: Consistent JSON responses with success/error states\n\n### Database Layer\n- Use Drizzle ORM for type-safe database operations\n- Implement storage interface abstraction\n- Define schemas in `shared/schema.ts`\n- Use Zod for input validation\n\n```typescript\n// ✅ Storage interface pattern\nexport interface IStorage {\n  getUser(id: number): Promise<User | undefined>;\n  getUserByUsername(username: string): Promise<User | undefined>;\n  createUser(user: InsertUser): Promise<User>;\n}\n\n// ✅ Implementation\nexport class MemStorage implements IStorage {\n  async getUser(id: number): Promise<User | undefined> {\n    return this.users.get(id);\n  }\n}\n```\n\n### Error Handling\n- Implement centralized error handling middleware\n- Use proper HTTP status codes\n- Return consistent error response format\n- Log errors appropriately\n\n## Styling & UI Guidelines\n\n### Tailwind CSS Standards\n- Use Tailwind utility classes for styling\n- Implement dark mode with `dark:` prefix\n- Use CSS variables for theme customization\n- Follow mobile-first responsive design\n\n### Component Styling Patterns\n```typescript\n// ✅ Tailwind class organization\n<button className={`\n  inline-flex items-center justify-center px-8 py-4\n  bg-gradient-to-r from-purple-600 to-blue-600\n  text-white font-semibold rounded-lg shadow-lg\n  hover:shadow-xl transform hover:-translate-y-1\n  transition-all duration-300\n`}>\n```\n\n### shadcn/ui Integration\n- Use shadcn/ui components as base building blocks\n- Customize components through Tailwind classes\n- Maintain consistent component API patterns\n- Follow Radix UI accessibility standards\n\n## Database & Schema Management\n\n### Schema Definition\n- Define all schemas in `shared/schema.ts`\n- Use Drizzle ORM table definitions\n- Implement Zod validation schemas\n- Export TypeScript types from schemas\n\n### Migration Strategy\n- Use Drizzle Kit for schema migrations\n- Run `npm run db:push` for development changes\n- Maintain DATABASE_URL environment variable\n- Use PostgreSQL with Neon serverless driver\n\n## Testing Conventions\n\n### Test Organization\n- Co-locate tests with components when possible\n- Use descriptive test names\n- Follow AAA pattern (Arrange, Act, Assert)\n- Test user interactions, not implementation details\n\n### Testing Tools\n- Use React Testing Library for component tests\n- Implement unit tests for utility functions\n- Use TypeScript in test files\n- Mock external dependencies appropriately\n\n## Build & Development Workflow\n\n### Development Commands\n```bash\nnpm run dev      # Start development server\nnpm run build    # Build for production\nnpm run check    # TypeScript type checking\nnpm run db:push  # Push database schema changes\n```\n\n### Build Configuration\n- Vite for frontend bundling and development\n- esbuild for backend production builds\n- TypeScript compilation with strict mode\n- Environment-specific configurations\n\n### Package Management\n- Use npm for dependency management\n- Keep dependencies up to date\n- Separate devDependencies from runtime dependencies\n- Use exact versions for critical dependencies\n\n## Git & Version Control\n\n### Commit Message Format\n```\ntype(scope): description\n\nExamples:\nfeat(auth): add user authentication\nfix(ui): resolve header navigation issue\ndocs(readme): update installation instructions\nrefactor(api): simplify user service logic\n```\n\n### Branch Strategy\n- Use descriptive branch names\n- Prefix with feature/, fix/, or docs/\n- Keep branches focused and small\n- Delete merged branches\n\n### Code Review Guidelines\n- Review for TypeScript type safety\n- Check for proper error handling\n- Ensure consistent code style\n- Verify responsive design implementation\n- Test functionality across different screen sizes\n\n---\n\n## Service Offerings\n\n### Small Business Essentials\n- **Target Audience**: Small businesses needing essential digital services\n- **Service Categories**:\n  - Cloud hosting setup\n  - Contact & booking forms\n  - Website speed optimization\n  - Bug fixes and maintenance\n  - Email & domain configuration\n- **Quick Quote System**: Streamlined multi-step form for rapid quote generation\n- **Service Delivery**: Focus on affordable, reliable solutions for small business needs\n\n---\n\n## Recent Updates\n\n### Location Information\n- **Company Location**: Margaret River and Canberra, Australia\n- **Footer Text**: \"Made with ❤️ in Margaret River and Canberra, Australia\"\n- **Contact Information**: Updated across Footer and Contact components\n- **Phone Number**: +61 8 9757 2000 (Australian number)\n- **Chamber Membership**: Proud member of the Margaret River Chamber of Commerce\n\n### Interactive Features Implementation\n- **Contact Form**: Full functionality with validation, loading states, and Resend email integration\n- **Newsletter Subscription**: Complete Resend integration with welcome emails and admin notifications\n- **Quick Quote Modal**: Multi-step modal form for Small Business Essentials service with smooth animations\n- **Live Chat**: Botpress chatbot integration with bubble widget and functional \"Start Live Chat\" button\n- **Navigation**: Smooth scrolling to sections with proper anchor handling\n- **CTA Buttons**: All call-to-action buttons now functional and navigate to contact form, quick quote, or live chat\n- **Form Validation**: Real-time validation with error messages and accessibility features\n- **Loading States**: Proper loading indicators for all async operations\n- **Modal System**: Accessible modal with keyboard navigation, backdrop click handling, and focus management\n\n### Email Service Integration (Resend)\n- **Service Provider**: Resend.com - Simple, reliable email API\n- **Contact Form System**: Dual emails (confirmation + notification) with full form data\n- **Newsletter System**: Welcome emails + admin notifications for new subscribers\n- **Domain Integration**: <NAME_EMAIL> for all emails\n- **HTML Templates**: Beautiful responsive email templates with beam.tech branding\n- **Security**: API token stored in environment variables, not client-side\n- **Error Handling**: Comprehensive error handling with user-friendly messages\n- **Production Ready**: All forms tested and working with Message IDs confirmed\n\n### Text File Logging System\n- **Contact Form Logging**: Tracks all submissions in `server/logs/contact-form-submissions.txt`\n- **Newsletter Logging**: Tracks all subscriptions in `server/logs/newsletter-subscriptions.txt`\n- **Simple Format**: Human-readable text format with structured data per line\n- **Timestamp Format**: ISO 8601 timestamps for consistent date/time tracking\n- **Status Tracking**: Logs both successful and failed submissions with detailed debugging\n- **File Management**: Auto-creates log files and directories on server startup\n- **Error Resilience**: Logging failures don't break email functionality\n- **Append-Only**: New entries are appended, existing data is preserved\n- **Production Ready**: Fully tested and working with comprehensive debugging output\n\n### Quick Quote Modal Implementation\n- **Component Structure**: `QuickQuoteModal.tsx` with multi-step form logic\n- **State Management**: Uses `useState` for step navigation and `useForm` for form handling\n- **Validation**: Real-time validation with `validateQuickQuoteForm` function\n- **API Integration**: Submits to `/api/quick-quote` endpoint with full error handling\n- **Email Templates**: Confirmation and notification emails with responsive HTML design\n- **Accessibility**: Keyboard navigation, focus management, and screen reader support\n- **Animation**: Smooth slide-in/fade-in transitions with Tailwind CSS\n- **Mobile Responsive**: Optimized for all screen sizes with proper modal sizing\n\n### Botpress Chatbot Integration\n- **Implementation**: Bubble chat widget integrated site-wide via HTML script tags\n- **Scripts**: Uses Botpress v3.0 webchat injection with custom configuration\n- **Utility Functions**: `client/src/utils/chatbot.ts` provides openChat, closeChat, toggleChat functions\n- **Contact Integration**: \"Start Live Chat\" button in Contact component opens Botpress chat\n- **Global Availability**: Chat bubble appears on all pages without interfering with modals or forms\n- **Browser Compatibility**: Works across modern browsers with fallback error handling\n\n### Component Simplification\n- **TechStack Component**: Simplified by removing technology category cards (Frontend, Backend, AI/ML, Cloud & DevOps)\n- **Focus on Value**: Maintained Australian service guarantees section while reducing technical complexity\n- **Clean Design**: Streamlined layout focuses on business value rather than technical details\n- **Performance**: Reduced component complexity improves page load and rendering performance\n\n### Australian-Focused Value Propositions\n- **Trust Indicators**: \"100% Australian-owned and operated\" prominently displayed\n- **Local Presence**: Margaret River & Canberra locations highlighted throughout\n- **Transparency Promises**: \"Fixed-price quotes. No hidden fees—ever\"\n- **Data Sovereignty**: \"Your data stays onshore: Australian data centres only\"\n- **Service Guarantees**: \"24-hour response time on all support tickets\"\n- **Professional Approach**: \"Free discovery call with a senior engineer, not a salesperson\"\n- **Plain Communication**: \"Plain-English agreements - no nonsense\"\n- **Scope Management**: \"If scope changes, we re-quote—never surprise-bill\"\n- **Experience Claims**: \"Over 40 years of combined web & cloud experience\"\n- **Geographic Coverage**: \"Serving small businesses from the South West to the ACT\"\n\n---\n\n## Quick Reference\n\n### Essential Commands\n- `npm run dev` - Start development server (port 5000)\n- `npm run build` - Production build\n- `npm run check` - Type checking\n- `npm run db:push` - Update database schema\n\n### Testing & Verification\n- **API Testing**: Use curl to test endpoints (`curl -X POST http://localhost:5000/api/quick-quote`)\n- **Form Validation**: Test all form fields with invalid/valid data\n- **Modal Functionality**: Verify modal opens, closes, and handles keyboard navigation\n- **Email Integration**: Check server logs for email sending status\n- **CSV Logging**: Verify log files are created and populated correctly\n- **Responsive Design**: Test on different screen sizes and devices\n\n### Key Patterns\n- Functional components with TypeScript\n- Custom hooks for shared logic\n- Tailwind for styling with dark mode\n- Drizzle ORM for database operations\n- Zod for runtime validation\n\n### File Extensions\n- `.tsx` for React components\n- `.ts` for TypeScript utilities\n- `.css` for global styles (minimal usage)\n"}