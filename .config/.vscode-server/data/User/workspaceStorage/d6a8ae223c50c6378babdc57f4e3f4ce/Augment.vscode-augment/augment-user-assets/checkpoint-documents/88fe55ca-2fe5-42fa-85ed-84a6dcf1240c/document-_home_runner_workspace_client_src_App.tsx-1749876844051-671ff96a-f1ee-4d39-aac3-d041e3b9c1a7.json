{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/App.tsx"}, "originalCode": "import React from 'react';\nimport Header from './components/Header';\nimport Hero from './components/Hero';\nimport Services from './components/Services';\nimport Portfolio from './components/Portfolio';\nimport TechStack from './components/TechStack';\nimport Testimonials from './components/Testimonials';\nimport Contact from './components/Contact';\nimport Footer from './components/Footer';\nimport ChatBubble from './components/ChatBubble';\n\nfunction App() {\n  return (\n    <div className=\"min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300\">\n      <Header />\n      <main>\n        <Hero />\n        <Services />\n        <Portfolio />\n        <TechStack />\n        <Testimonials />\n        <Contact />\n      </main>\n      <Footer />\n    </div>\n  );\n}\n\nexport default App;", "modifiedCode": "import React from 'react';\nimport Header from './components/Header';\nimport Hero from './components/Hero';\nimport Services from './components/Services';\nimport Portfolio from './components/Portfolio';\nimport TechStack from './components/TechStack';\nimport Testimonials from './components/Testimonials';\nimport Contact from './components/Contact';\nimport Footer from './components/Footer';\nimport ChatBubble from './components/ChatBubble';\n\nfunction App() {\n  return (\n    <div className=\"min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300\">\n      <Header />\n      <main>\n        <Hero />\n        <Services />\n        <Portfolio />\n        <TechStack />\n        <Testimonials />\n        <Contact />\n      </main>\n      <Footer />\n    </div>\n  );\n}\n\nexport default App;"}