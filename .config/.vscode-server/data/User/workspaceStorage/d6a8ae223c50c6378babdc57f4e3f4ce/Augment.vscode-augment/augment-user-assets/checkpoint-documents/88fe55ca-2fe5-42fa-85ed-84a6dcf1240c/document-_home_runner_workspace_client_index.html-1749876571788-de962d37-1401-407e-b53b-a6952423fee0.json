{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/index.html"}, "originalCode": "<!DOCTYPE html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1\" />\n    <title>beam.tech - Australian Web Solutions</title>\n\n    <!-- Botpress Chatbot Integration -->\n    <script src=\"https://cdn.botpress.cloud/webchat/v3.0/inject.js\"></script>\n    <script>\n      // Wait for Botpress to be available before initializing\n      (function() {\n        function initBotpress() {\n          if (window.botpress) {\n            window.botpress.init({\n              \"botId\": \"8b4fa29e-1545-4fde-9991-8be5f36a824c\",\n              \"configuration\": {\n                \"version\": \"v1\",\n                \"website\": {\n                  \"title\": \"https://beamtradingx.com/\",\n                  \"link\": \"https://beamtradingx.com/\"\n                },\n                \"email\": {\n                  \"title\": \"<EMAIL>\",\n                  \"link\": \"<EMAIL>\"\n                },\n                \"phone\": {},\n                \"termsOfService\": {\n                  \"title\": \"Terms of service\",\n                  \"link\": \"https://beamtradingx.com/\"\n                },\n                \"privacyPolicy\": {\n                  \"title\": \"Privacy policy\",\n                  \"link\": \"https://beamtradingx.com/\"\n                },\n                \"color\": \"#9333ea\",\n                \"variant\": \"solid\",\n                \"headerVariant\": \"solid\",\n                \"themeMode\": \"light\",\n                \"fontFamily\": \"inter\",\n                \"radius\": 4,\n                \"feedbackEnabled\": false,\n                \"footer\": \"[⚡ by Botpress](https://botpress.com/?from=webchat)\",\n                \"additionalStylesheetUrl\": \"https://files.bpcontent.cloud/2025/06/14/04/20250614044255-FAH0974Q.css\"\n              },\n              \"clientId\": \"58988b78-12d9-40bd-aa18-305bed1f1fa7\"\n            });\n          } else {\n            // Retry after 100ms if Botpress isn't loaded yet\n            setTimeout(initBotpress, 100);\n          }\n        }\n\n        // Start initialization when DOM is ready\n        if (document.readyState === 'loading') {\n          document.addEventListener('DOMContentLoaded', initBotpress);\n        } else {\n          initBotpress();\n        }\n      })();\n    </script>\n  </head>\n  <body>\n    <div id=\"root\"></div>\n    <script type=\"module\" src=\"/src/main.tsx\"></script>\n    <!-- This is a replit script which adds a banner on the top of the page when opened in development mode outside the replit environment -->\n    <script type=\"text/javascript\" src=\"https://replit.com/public/js/replit-dev-banner.js\"></script>\n  </body>\n</html>", "modifiedCode": "<!DOCTYPE html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1\" />\n    <title>beam.tech - Australian Web Solutions</title>\n\n    <!-- Botpress Chatbot Integration -->\n    <script src=\"https://cdn.botpress.cloud/webchat/v3.0/inject.js\"></script>\n    <script>\n      // Wait for Botpress to be available before initializing\n      (function() {\n        function initBotpress() {\n          if (window.botpress) {\n            window.botpress.init({\n              \"botId\": \"8b4fa29e-1545-4fde-9991-8be5f36a824c\",\n              \"configuration\": {\n                \"version\": \"v1\",\n                \"website\": {\n                  \"title\": \"https://beamtradingx.com/\",\n                  \"link\": \"https://beamtradingx.com/\"\n                },\n                \"email\": {\n                  \"title\": \"<EMAIL>\",\n                  \"link\": \"<EMAIL>\"\n                },\n                \"phone\": {},\n                \"termsOfService\": {\n                  \"title\": \"Terms of service\",\n                  \"link\": \"https://beamtradingx.com/\"\n                },\n                \"privacyPolicy\": {\n                  \"title\": \"Privacy policy\",\n                  \"link\": \"https://beamtradingx.com/\"\n                },\n                \"color\": \"#9333ea\",\n                \"variant\": \"solid\",\n                \"headerVariant\": \"solid\",\n                \"themeMode\": \"light\",\n                \"fontFamily\": \"inter\",\n                \"radius\": 4,\n                \"feedbackEnabled\": false,\n                \"footer\": \"[⚡ by Botpress](https://botpress.com/?from=webchat)\",\n                \"additionalStylesheetUrl\": \"https://files.bpcontent.cloud/2025/06/14/04/20250614044255-FAH0974Q.css\"\n              },\n              \"clientId\": \"58988b78-12d9-40bd-aa18-305bed1f1fa7\"\n            });\n          } else {\n            // Retry after 100ms if Botpress isn't loaded yet\n            setTimeout(initBotpress, 100);\n          }\n        }\n\n        // Start initialization when DOM is ready\n        if (document.readyState === 'loading') {\n          document.addEventListener('DOMContentLoaded', initBotpress);\n        } else {\n          initBotpress();\n        }\n      })();\n    </script>\n  </head>\n  <body>\n    <div id=\"root\"></div>\n    <script type=\"module\" src=\"/src/main.tsx\"></script>\n    <!-- This is a replit script which adds a banner on the top of the page when opened in development mode outside the replit environment -->\n    <script type=\"text/javascript\" src=\"https://replit.com/public/js/replit-dev-banner.js\"></script>\n  </body>\n</html>"}