{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/utils/chatbot.ts"}, "modifiedCode": "// Botpress chatbot utilities\n\ndeclare global {\n  interface Window {\n    botpress?: {\n      open: () => void;\n      close: () => void;\n      toggle: () => void;\n      isOpen: () => boolean;\n    };\n  }\n}\n\n/**\n * Opens the Botpress chatbot\n */\nexport const openChat = (): void => {\n  if (window.botpress) {\n    window.botpress.open();\n  } else {\n    console.warn('Botpress chatbot not loaded yet');\n  }\n};\n\n/**\n * Closes the Botpress chatbot\n */\nexport const closeChat = (): void => {\n  if (window.botpress) {\n    window.botpress.close();\n  }\n};\n\n/**\n * Toggles the Botpress chatbot\n */\nexport const toggleChat = (): void => {\n  if (window.botpress) {\n    window.botpress.toggle();\n  } else {\n    console.warn('Botpress chatbot not loaded yet');\n  }\n};\n\n/**\n * Checks if the Botpress chatbot is open\n */\nexport const isChatOpen = (): boolean => {\n  if (window.botpress) {\n    return window.botpress.isOpen();\n  }\n  return false;\n};\n\n/**\n * Waits for Botpress to be loaded and then executes a callback\n */\nexport const whenChatReady = (callback: () => void): void => {\n  const checkBotpress = () => {\n    if (window.botpress) {\n      callback();\n    } else {\n      setTimeout(checkBotpress, 100);\n    }\n  };\n  checkBotpress();\n};\n"}