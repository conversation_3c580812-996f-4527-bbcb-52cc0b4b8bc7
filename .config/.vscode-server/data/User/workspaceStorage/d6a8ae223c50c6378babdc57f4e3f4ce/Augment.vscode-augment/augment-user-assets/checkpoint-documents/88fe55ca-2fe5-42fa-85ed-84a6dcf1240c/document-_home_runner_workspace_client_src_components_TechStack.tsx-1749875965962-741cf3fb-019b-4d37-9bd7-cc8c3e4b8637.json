{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/TechStack.tsx"}, "originalCode": "import React from 'react';\nimport { Globe } from 'lucide-react';\n\nconst TechStack: React.FC = () => {\n\n  return (\n    <section id=\"tech\" className=\"py-20 bg-white dark:bg-gray-900\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center max-w-3xl mx-auto mb-16\">\n          <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\">\n            Our <span className=\"bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent\">Technology Stack</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300\">\n            We leverage cutting-edge technologies to build scalable, performant, and future-ready solutions\n          </p>\n        </div>\n\n        {/* Technology Categories */}\n        <div className=\"grid lg:grid-cols-2 gap-8 mb-16\">\n          {categories.map((category) => {\n            const IconComponent = category.icon;\n            return (\n              <div\n                key={category.id}\n                className=\"bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-gray-700\"\n              >\n                {/* Category Header */}\n                <div className=\"flex items-center space-x-4 mb-6\">\n                  <div className={`p-3 bg-gradient-to-r ${category.color} rounded-xl`}>\n                    <IconComponent className=\"h-8 w-8 text-white\" />\n                  </div>\n                  <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {category.title}\n                  </h3>\n                </div>\n\n                {/* Technologies Grid */}\n                <div className=\"grid grid-cols-2 gap-4\">\n                  {category.technologies.map((tech, index) => (\n                    <div\n                      key={index}\n                      className=\"group p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200\"\n                    >\n                      <div className=\"flex items-start space-x-3\">\n                        <div className={`w-2 h-2 bg-gradient-to-r ${category.color} rounded-full mt-2 flex-shrink-0`}></div>\n                        <div>\n                          <h4 className=\"font-semibold text-gray-900 dark:text-white text-sm\">\n                            {tech.name}\n                          </h4>\n                          <p className=\"text-xs text-gray-600 dark:text-gray-300 mt-1\">\n                            {tech.description}\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Australian Service Guarantees */}\n        <div className=\"bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-8 text-white\">\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 text-center\">\n            <div>\n              <div className=\"text-2xl font-bold mb-2\">40+ Years</div>\n              <div className=\"text-purple-100\">Combined Experience</div>\n            </div>\n            <div>\n              <div className=\"text-2xl font-bold mb-2\">Australian</div>\n              <div className=\"text-purple-100\">Data Centres Only</div>\n            </div>\n            <div>\n              <div className=\"text-2xl font-bold mb-2\">24/7</div>\n              <div className=\"text-purple-100\">Australian Support</div>\n            </div>\n            <div>\n              <div className=\"text-2xl font-bold mb-2\">No Hidden</div>\n              <div className=\"text-purple-100\">Fees—Ever</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"text-center mt-16\">\n          <div className=\"inline-flex items-center space-x-2 bg-gray-100 dark:bg-gray-800 rounded-full px-6 py-3 mb-6\">\n            <Globe className=\"h-5 w-5 text-purple-600 dark:text-purple-400\" />\n            <span className=\"text-gray-700 dark:text-gray-300 font-medium\">Always Learning, Always Innovating</span>\n          </div>\n          <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\">\n            Our team stays at the forefront of technology, continuously learning and adopting new tools to deliver the best solutions for our clients.\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default TechStack;", "modifiedCode": "import React from 'react';\nimport { Globe } from 'lucide-react';\n\nconst TechStack: React.FC = () => {\n\n  return (\n    <section id=\"tech\" className=\"py-20 bg-white dark:bg-gray-900\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center max-w-3xl mx-auto mb-16\">\n          <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\">\n            Our <span className=\"bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent\">Technology Stack</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300\">\n            We leverage cutting-edge technologies to build scalable, performant, and future-ready solutions\n          </p>\n        </div>\n\n        {/* Technology Categories */}\n        <div className=\"grid lg:grid-cols-2 gap-8 mb-16\">\n          {categories.map((category) => {\n            const IconComponent = category.icon;\n            return (\n              <div\n                key={category.id}\n                className=\"bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-gray-700\"\n              >\n                {/* Category Header */}\n                <div className=\"flex items-center space-x-4 mb-6\">\n                  <div className={`p-3 bg-gradient-to-r ${category.color} rounded-xl`}>\n                    <IconComponent className=\"h-8 w-8 text-white\" />\n                  </div>\n                  <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {category.title}\n                  </h3>\n                </div>\n\n                {/* Technologies Grid */}\n                <div className=\"grid grid-cols-2 gap-4\">\n                  {category.technologies.map((tech, index) => (\n                    <div\n                      key={index}\n                      className=\"group p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200\"\n                    >\n                      <div className=\"flex items-start space-x-3\">\n                        <div className={`w-2 h-2 bg-gradient-to-r ${category.color} rounded-full mt-2 flex-shrink-0`}></div>\n                        <div>\n                          <h4 className=\"font-semibold text-gray-900 dark:text-white text-sm\">\n                            {tech.name}\n                          </h4>\n                          <p className=\"text-xs text-gray-600 dark:text-gray-300 mt-1\">\n                            {tech.description}\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Australian Service Guarantees */}\n        <div className=\"bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-8 text-white\">\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 text-center\">\n            <div>\n              <div className=\"text-2xl font-bold mb-2\">40+ Years</div>\n              <div className=\"text-purple-100\">Combined Experience</div>\n            </div>\n            <div>\n              <div className=\"text-2xl font-bold mb-2\">Australian</div>\n              <div className=\"text-purple-100\">Data Centres Only</div>\n            </div>\n            <div>\n              <div className=\"text-2xl font-bold mb-2\">24/7</div>\n              <div className=\"text-purple-100\">Australian Support</div>\n            </div>\n            <div>\n              <div className=\"text-2xl font-bold mb-2\">No Hidden</div>\n              <div className=\"text-purple-100\">Fees—Ever</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"text-center mt-16\">\n          <div className=\"inline-flex items-center space-x-2 bg-gray-100 dark:bg-gray-800 rounded-full px-6 py-3 mb-6\">\n            <Globe className=\"h-5 w-5 text-purple-600 dark:text-purple-400\" />\n            <span className=\"text-gray-700 dark:text-gray-300 font-medium\">Always Learning, Always Innovating</span>\n          </div>\n          <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\">\n            Our team stays at the forefront of technology, continuously learning and adopting new tools to deliver the best solutions for our clients.\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default TechStack;"}