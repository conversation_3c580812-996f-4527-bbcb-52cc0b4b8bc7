[["/home/<USER>/workspace/test-text-logging.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "test-text-logging.js"}}], ["/home/<USER>/workspace/server/index.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "server/index.ts"}}], ["/home/<USER>/workspace/client/src/components/Footer.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Footer.tsx"}}], ["/home/<USER>/workspace/server/csvLogger.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "server/csvLogger.ts"}}], ["/home/<USER>/workspace/test-logging.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "test-logging.js"}}], ["/home/<USER>/workspace/test-api.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "test-api.js"}}], ["/home/<USER>/workspace/server/test-logging.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "server/test-logging.ts"}}], ["/home/<USER>/workspace/server/routes.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes.ts"}}], ["/home/<USER>/workspace/augment-guidelines.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "augment-guidelines.md"}}]]