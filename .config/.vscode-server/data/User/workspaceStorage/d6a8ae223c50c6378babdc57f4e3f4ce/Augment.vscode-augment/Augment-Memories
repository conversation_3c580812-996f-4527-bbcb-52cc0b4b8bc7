# Coding Standards
- User wants augment-guidelines.md to be updated after every change to maintain consistent coding standards and ensure clean, non-duplicative layout of rules.

# Website Appearance
- User prefers footer text to be 'Made with ❤️ in Margaret River and Canberra, Australia' rather than just 'Made in' - wants to keep the heart emoji.
- User prefers simplified UI by removing technology category cards while keeping service guarantees sections.

# Lead Generation
- User prioritizes contact form and newsletter signup functionality as critical for lead generation and requires production-ready implementation with proper validation, email services, loading states, and accessibility features.
- User prefers CSV logging for form submissions with proper formatting, error handling, and server-side storage for contact forms and newsletter subscriptions.
- User prefers multi-step modal forms with progress indicators, smooth animations (fade-in/slide-in), and specific service categorization for small business offerings including cloud hosting, forms, performance tweaks, and domain setup.

# Chatbot Integration
- User prefers Botpress chatbot integration with Bubble option for site-wide availability.

# Branding & Service Guarantees
- User prefers Australian-focused branding emphasizing Margaret River & Canberra locations, transparency (fixed-price quotes, no hidden fees), local data hosting, and specific service guarantees like 24-hour response times and free discovery calls.