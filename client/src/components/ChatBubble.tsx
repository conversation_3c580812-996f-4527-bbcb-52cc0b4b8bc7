import React, { useState } from 'react';
import { MessageCircle, X, Send } from 'lucide-react';

const ChatBubble: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState('');

  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim()) {
      // For now, just show an alert. This can be replaced with actual chat functionality
      alert(`Thanks for your message: "${message}". We'll get back to you soon! For immediate assistance, please call +61 8 9757 2000 <NAME_EMAIL>`);
      setMessage('');
      setIsOpen(false);
    }
  };

  return (
    <>
      {/* Chat Bubble */}
      <div className="fixed bottom-6 right-6 z-50">
        <button
          onClick={toggleChat}
          className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 focus:outline-none focus:ring-4 focus:ring-purple-300"
          aria-label="Open chat"
        >
          {isOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <MessageCircle className="h-6 w-6" />
          )}
        </button>
      </div>

      {/* Chat Window */}
      {isOpen && (
        <div className="fixed bottom-24 right-6 w-80 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 z-50 overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold">beam.tech Support</h3>
                <p className="text-sm text-purple-100">We're here to help!</p>
              </div>
              <button
                onClick={toggleChat}
                className="text-white hover:text-purple-200 transition-colors duration-200"
                aria-label="Close chat"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Chat Content */}
          <div className="p-4 h-64 overflow-y-auto">
            <div className="space-y-4">
              {/* Welcome Message */}
              <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
                <p className="text-sm text-gray-800 dark:text-gray-200">
                  👋 Hi there! Welcome to beam.tech. How can we help you today?
                </p>
              </div>
              
              {/* Quick Actions */}
              <div className="space-y-2">
                <p className="text-xs text-gray-600 dark:text-gray-400 font-medium">Quick Actions:</p>
                <button
                  onClick={() => {
                    setMessage('I need a quote for Small Business Essentials');
                  }}
                  className="w-full text-left text-sm bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 p-2 rounded hover:bg-purple-100 dark:hover:bg-purple-900/40 transition-colors duration-200"
                >
                  💼 Get a quote for Small Business Essentials
                </button>
                <button
                  onClick={() => {
                    setMessage('I want to schedule a free discovery call');
                  }}
                  className="w-full text-left text-sm bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 p-2 rounded hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors duration-200"
                >
                  📞 Schedule a free discovery call
                </button>
                <button
                  onClick={() => {
                    setMessage('I have a question about your services');
                  }}
                  className="w-full text-left text-sm bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 p-2 rounded hover:bg-green-100 dark:hover:bg-green-900/40 transition-colors duration-200"
                >
                  ❓ Ask about our services
                </button>
              </div>

              {/* Contact Info */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-xs text-gray-600 dark:text-gray-400">
                <p className="font-medium mb-1">Direct Contact:</p>
                <p>📧 <EMAIL></p>
                <p>📞 +61 8 9757 2000</p>
                <p>🕒 24-hour response guarantee</p>
              </div>
            </div>
          </div>

          {/* Message Input */}
          <form onSubmit={handleSubmit} className="p-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex space-x-2">
              <input
                type="text"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Type your message..."
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
              <button
                type="submit"
                disabled={!message.trim()}
                className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-2 rounded-lg hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                aria-label="Send message"
              >
                <Send className="h-4 w-4" />
              </button>
            </div>
          </form>
        </div>
      )}
    </>
  );
};

export default ChatBubble;
