// Botpress chatbot utilities

declare global {
  interface Window {
    botpress?: {
      open: () => void;
      close: () => void;
      toggle: () => void;
      isOpen: () => boolean;
    };
  }
}

/**
 * Opens the Botpress chatbot
 */
export const openChat = (): void => {
  if (window.botpress) {
    window.botpress.open();
  } else {
    console.warn('Botpress chatbot not loaded yet');
  }
};

/**
 * Closes the Botpress chatbot
 */
export const closeChat = (): void => {
  if (window.botpress) {
    window.botpress.close();
  }
};

/**
 * Toggles the Botpress chatbot
 */
export const toggleChat = (): void => {
  if (window.botpress) {
    window.botpress.toggle();
  } else {
    console.warn('Botpress chatbot not loaded yet');
  }
};

/**
 * Checks if the Botpress chatbot is open
 */
export const isChatOpen = (): boolean => {
  if (window.botpress) {
    return window.botpress.isOpen();
  }
  return false;
};

/**
 * Waits for Botpress to be loaded and then executes a callback
 */
export const whenChatReady = (callback: () => void): void => {
  const checkBotpress = () => {
    if (window.botpress) {
      callback();
    } else {
      setTimeout(checkBotpress, 100);
    }
  };
  checkBotpress();
};
