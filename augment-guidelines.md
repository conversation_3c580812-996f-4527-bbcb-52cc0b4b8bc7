# Augment Development Guidelines

## Table of Contents
1. [Project Overview](#project-overview)
2. [Code Structure & Organization](#code-structure--organization)
3. [TypeScript Standards](#typescript-standards)
4. [React Development Conventions](#react-development-conventions)
5. [Backend Development Standards](#backend-development-standards)
6. [Styling & UI Guidelines](#styling--ui-guidelines)
7. [Database & Schema Management](#database--schema-management)
8. [Testing Conventions](#testing-conventions)
9. [Build & Development Workflow](#build--development-workflow)
10. [Git & Version Control](#git--version-control)

## Project Overview

This is a modern full-stack TypeScript application built with:
- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS + shadcn/ui
- **Backend**: Express.js + TypeScript + Drizzle ORM + PostgreSQL
- **Architecture**: Monorepo with clear client/server/shared separation
- **Deployment**: Replit with autoscale configuration

## Code Structure & Organization

### Directory Structure
```
/
├── client/                 # Frontend React application
│   ├── src/
│   │   ├── components/     # React components (PascalCase)
│   │   ├── hooks/          # Custom React hooks (camelCase, use prefix)
│   │   ├── types/          # TypeScript type definitions
│   │   └── main.tsx        # Application entry point
│   └── index.html          # HTML template
├── server/                 # Backend Express application
│   ├── index.ts           # Server entry point
│   ├── routes.ts          # API route definitions
│   ├── storage.ts         # Database abstraction layer
│   └── vite.ts            # Development server configuration
├── shared/                # Shared code between client/server
│   └── schema.ts          # Database schema & validation
└── [config files]         # Build and configuration files
```

### File Naming Conventions
- **React Components**: PascalCase (e.g., `Header.tsx`, `UserProfile.tsx`)
- **Hooks**: camelCase with `use` prefix (e.g., `useTheme.tsx`, `useAuth.tsx`)
- **Utilities**: camelCase (e.g., `apiClient.ts`, `formatDate.ts`)
- **Types/Interfaces**: PascalCase (e.g., `User.ts`, `ApiResponse.ts`)
- **Server Files**: camelCase (e.g., `routes.ts`, `storage.ts`)

### Import/Export Standards
- Use named exports for components and utilities
- Use default exports only for main component files
- Group imports: external libraries first, then internal modules
- Use path aliases: `@/` for client src, `@shared/` for shared code

```typescript
// ✅ Good import order
import React, { useState, useEffect } from 'react';
import { Button } from '@radix-ui/react-button';
import { useTheme } from '@/hooks/useTheme';
import { User } from '@shared/schema';

// ✅ Named exports preferred
export const Header: React.FC = () => { ... };
export { Header as default };
```

## TypeScript Standards

### Type Definitions
- Define interfaces in `types/` directory or co-located with components
- Use PascalCase for interfaces and types
- Prefer interfaces over type aliases for object shapes
- Use strict TypeScript configuration

```typescript
// ✅ Interface definition
export interface User {
  id: number;
  username: string;
  email?: string;
}

// ✅ Component props interface
interface HeaderProps {
  title: string;
  onMenuClick?: () => void;
}
```

### Type Safety Practices
- Always type function parameters and return values
- Use `React.FC` for functional components
- Leverage TypeScript's strict mode
- Use Zod for runtime validation of external data

```typescript
// ✅ Properly typed component
const Header: React.FC<HeaderProps> = ({ title, onMenuClick }) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  
  const handleClick = (): void => {
    setIsOpen(!isOpen);
    onMenuClick?.();
  };

  return <header>{title}</header>;
};
```

## React Development Conventions

### Component Structure
- Use functional components with hooks
- Implement proper TypeScript typing
- Follow single responsibility principle
- Use descriptive component and prop names

### State Management
- Use `useState` for local component state
- Use custom hooks for shared stateful logic
- Implement proper dependency arrays in `useEffect`
- Use `useCallback` and `useMemo` for performance optimization when needed

### Form Management
- Use `useForm` hook for complex form handling with validation
- Implement proper error states and user feedback
- Add loading states for async operations
- Use TypeScript interfaces for form data types
- **Multi-Step Forms**: Implement step-by-step validation and navigation
- **Conditional Fields**: Show/hide fields based on user selections
- **Progress Indicators**: Visual feedback for multi-step processes

```typescript
// ✅ Custom hook example
export const useTheme = () => {
  const [isDark, setIsDark] = useState<boolean>(false);

  useEffect(() => {
    const saved = localStorage.getItem('theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    if (saved === 'dark' || (!saved && prefersDark)) {
      setIsDark(true);
      document.documentElement.classList.add('dark');
    }
  }, []);

  const toggleTheme = useCallback((): void => {
    setIsDark(prev => {
      const newValue = !prev;
      if (newValue) {
        document.documentElement.classList.add('dark');
        localStorage.setItem('theme', 'dark');
      } else {
        document.documentElement.classList.remove('dark');
        localStorage.setItem('theme', 'light');
      }
      return newValue;
    });
  }, []);

  return { isDark, toggleTheme };
};
```

### Event Handling
- Use descriptive handler names with `handle` prefix
- Implement proper TypeScript event typing
- Use arrow functions for inline handlers when appropriate

### Utility Functions
- Create reusable utility functions in `utils/` directory
- Use proper TypeScript typing for all utilities
- Implement validation utilities with Zod integration
- Create navigation utilities for smooth scrolling
- Build email service abstractions for form submissions

## Backend Development Standards

### API Structure
- Prefix all API routes with `/api`
- Use RESTful conventions for route naming
- Implement proper error handling middleware
- Use TypeScript for all server code
- **Endpoint Patterns**: `/api/contact`, `/api/newsletter`, `/api/quick-quote`
- **Request Validation**: Validate required fields before processing
- **Response Format**: Consistent JSON responses with success/error states

### Database Layer
- Use Drizzle ORM for type-safe database operations
- Implement storage interface abstraction
- Define schemas in `shared/schema.ts`
- Use Zod for input validation

```typescript
// ✅ Storage interface pattern
export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
}

// ✅ Implementation
export class MemStorage implements IStorage {
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }
}
```

### Error Handling
- Implement centralized error handling middleware
- Use proper HTTP status codes
- Return consistent error response format
- Log errors appropriately

## Styling & UI Guidelines

### Tailwind CSS Standards
- Use Tailwind utility classes for styling
- Implement dark mode with `dark:` prefix
- Use CSS variables for theme customization
- Follow mobile-first responsive design

### Component Styling Patterns
```typescript
// ✅ Tailwind class organization
<button className={`
  inline-flex items-center justify-center px-8 py-4
  bg-gradient-to-r from-purple-600 to-blue-600
  text-white font-semibold rounded-lg shadow-lg
  hover:shadow-xl transform hover:-translate-y-1
  transition-all duration-300
`}>
```

### shadcn/ui Integration
- Use shadcn/ui components as base building blocks
- Customize components through Tailwind classes
- Maintain consistent component API patterns
- Follow Radix UI accessibility standards

## Database & Schema Management

### Schema Definition
- Define all schemas in `shared/schema.ts`
- Use Drizzle ORM table definitions
- Implement Zod validation schemas
- Export TypeScript types from schemas

### Migration Strategy
- Use Drizzle Kit for schema migrations
- Run `npm run db:push` for development changes
- Maintain DATABASE_URL environment variable
- Use PostgreSQL with Neon serverless driver

## Testing Conventions

### Test Organization
- Co-locate tests with components when possible
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)
- Test user interactions, not implementation details

### Testing Tools
- Use React Testing Library for component tests
- Implement unit tests for utility functions
- Use TypeScript in test files
- Mock external dependencies appropriately

## Build & Development Workflow

### Development Commands
```bash
npm run dev      # Start development server
npm run build    # Build for production
npm run check    # TypeScript type checking
npm run db:push  # Push database schema changes
```

### Build Configuration
- Vite for frontend bundling and development
- esbuild for backend production builds
- TypeScript compilation with strict mode
- Environment-specific configurations

### Package Management
- Use npm for dependency management
- Keep dependencies up to date
- Separate devDependencies from runtime dependencies
- Use exact versions for critical dependencies

## Git & Version Control

### Commit Message Format
```
type(scope): description

Examples:
feat(auth): add user authentication
fix(ui): resolve header navigation issue
docs(readme): update installation instructions
refactor(api): simplify user service logic
```

### Branch Strategy
- Use descriptive branch names
- Prefix with feature/, fix/, or docs/
- Keep branches focused and small
- Delete merged branches

### Code Review Guidelines
- Review for TypeScript type safety
- Check for proper error handling
- Ensure consistent code style
- Verify responsive design implementation
- Test functionality across different screen sizes

---

## Service Offerings

### Small Business Essentials
- **Target Audience**: Small businesses needing essential digital services
- **Service Categories**:
  - Cloud hosting setup
  - Contact & booking forms
  - Website speed optimization
  - Bug fixes and maintenance
  - Email & domain configuration
- **Quick Quote System**: Streamlined multi-step form for rapid quote generation
- **Service Delivery**: Focus on affordable, reliable solutions for small business needs

---

## Recent Updates

### Location Information
- **Company Location**: Margaret River and Canberra, Australia
- **Footer Text**: "Made with ❤️ in Margaret River and Canberra, Australia"
- **Contact Information**: Updated across Footer and Contact components
- **Phone Number**: +61 8 9757 2000 (Australian number)
- **Chamber Membership**: Proud member of the Margaret River Chamber of Commerce

### Interactive Features Implementation
- **Contact Form**: Full functionality with validation, loading states, and Resend email integration
- **Newsletter Subscription**: Complete Resend integration with welcome emails and admin notifications
- **Quick Quote Modal**: Multi-step modal form for Small Business Essentials service with smooth animations
- **Live Chat**: Botpress chatbot integration with bubble widget and functional "Start Live Chat" button
- **Navigation**: Smooth scrolling to sections with proper anchor handling
- **CTA Buttons**: All call-to-action buttons now functional and navigate to contact form, quick quote, or live chat
- **Form Validation**: Real-time validation with error messages and accessibility features
- **Loading States**: Proper loading indicators for all async operations
- **Modal System**: Accessible modal with keyboard navigation, backdrop click handling, and focus management

### Email Service Integration (Resend)
- **Service Provider**: Resend.com - Simple, reliable email API
- **Contact Form System**: Dual emails (confirmation + notification) with full form data
- **Newsletter System**: Welcome emails + admin notifications for new subscribers
- **Domain Integration**: <NAME_EMAIL> for all emails
- **HTML Templates**: Beautiful responsive email templates with beam.tech branding
- **Security**: API token stored in environment variables, not client-side
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Production Ready**: All forms tested and working with Message IDs confirmed

### Text File Logging System
- **Contact Form Logging**: Tracks all submissions in `server/logs/contact-form-submissions.txt`
- **Newsletter Logging**: Tracks all subscriptions in `server/logs/newsletter-subscriptions.txt`
- **Simple Format**: Human-readable text format with structured data per line
- **Timestamp Format**: ISO 8601 timestamps for consistent date/time tracking
- **Status Tracking**: Logs both successful and failed submissions with detailed debugging
- **File Management**: Auto-creates log files and directories on server startup
- **Error Resilience**: Logging failures don't break email functionality
- **Append-Only**: New entries are appended, existing data is preserved
- **Production Ready**: Fully tested and working with comprehensive debugging output

### Quick Quote Modal Implementation
- **Component Structure**: `QuickQuoteModal.tsx` with multi-step form logic
- **State Management**: Uses `useState` for step navigation and `useForm` for form handling
- **Validation**: Real-time validation with `validateQuickQuoteForm` function
- **API Integration**: Submits to `/api/quick-quote` endpoint with full error handling
- **Email Templates**: Confirmation and notification emails with responsive HTML design
- **Accessibility**: Keyboard navigation, focus management, and screen reader support
- **Animation**: Smooth slide-in/fade-in transitions with Tailwind CSS
- **Mobile Responsive**: Optimized for all screen sizes with proper modal sizing

### Botpress Chatbot Integration
- **Implementation**: Bubble chat widget integrated site-wide via HTML script tags
- **Scripts**: Uses Botpress v3.0 webchat injection with custom configuration
- **Bot Configuration**:
  - Bot ID: `8b4fa29e-1545-4fde-9991-8be5f36a824c`
  - Client ID: `58988b78-12d9-40bd-aa18-305bed1f1fa7`
  - Color: `#9333ea` (purple theme matching site design)
- **Utility Functions**: `client/src/utils/chatbot.ts` provides openChat, closeChat, toggleChat functions
- **Contact Integration**: "Start Live Chat" button in Contact component opens Botpress chat
- **Initialization**: Scripts load in body with window.load event listener for proper timing
- **Troubleshooting**: Debug logging added to Contact button for testing
- **Status**: Implementation complete, may require bot to be published in Botpress dashboard
- **Fallback Solution**: Custom ChatBubble component provides immediate chat functionality
- **Custom Chat Features**:
  - Purple gradient bubble matching site design
  - Quick action buttons for common requests
  - Contact information display
  - Message input with form submission
  - Responsive design for mobile and desktop
  - Integrated with "Start Live Chat" button in Contact section

### Component Simplification
- **TechStack Component**: Simplified by removing technology category cards (Frontend, Backend, AI/ML, Cloud & DevOps)
- **Focus on Value**: Maintained Australian service guarantees section while reducing technical complexity
- **Clean Design**: Streamlined layout focuses on business value rather than technical details
- **Performance**: Reduced component complexity improves page load and rendering performance

### Australian-Focused Value Propositions
- **Trust Indicators**: "100% Australian-owned and operated" prominently displayed
- **Local Presence**: Margaret River & Canberra locations highlighted throughout
- **Transparency Promises**: "Fixed-price quotes. No hidden fees—ever"
- **Data Sovereignty**: "Your data stays onshore: Australian data centres only"
- **Service Guarantees**: "24-hour response time on all support tickets"
- **Professional Approach**: "Free discovery call with a senior engineer, not a salesperson"
- **Plain Communication**: "Plain-English agreements - no nonsense"
- **Scope Management**: "If scope changes, we re-quote—never surprise-bill"
- **Experience Claims**: "Over 40 years of combined web & cloud experience"
- **Geographic Coverage**: "Serving small businesses from the South West to the ACT"

---

## Quick Reference

### Essential Commands
- `npm run dev` - Start development server (port 5000)
- `npm run build` - Production build
- `npm run check` - Type checking
- `npm run db:push` - Update database schema

### Testing & Verification
- **API Testing**: Use curl to test endpoints (`curl -X POST http://localhost:5000/api/quick-quote`)
- **Form Validation**: Test all form fields with invalid/valid data
- **Modal Functionality**: Verify modal opens, closes, and handles keyboard navigation
- **Email Integration**: Check server logs for email sending status
- **CSV Logging**: Verify log files are created and populated correctly
- **Responsive Design**: Test on different screen sizes and devices

### Key Patterns
- Functional components with TypeScript
- Custom hooks for shared logic
- Tailwind for styling with dark mode
- Drizzle ORM for database operations
- Zod for runtime validation

### File Extensions
- `.tsx` for React components
- `.ts` for TypeScript utilities
- `.css` for global styles (minimal usage)
